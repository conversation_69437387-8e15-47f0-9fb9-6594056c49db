"""
管理系统数据模型
"""
from tortoise.models import Model
from tortoise import fields
from enum import Enum
from datetime import datetime
from typing import Optional


class MenuType(str, Enum):
    CATALOG = "catalog"
    MENU = "menu"
    BUTTON = "button"


class TimestampMixin:
    created_at = fields.DatetimeField(auto_now_add=True, description="创建时间")
    updated_at = fields.DatetimeField(auto_now=True, description="更新时间")


class User(Model, TimestampMixin):
    """用户模型"""
    id = fields.IntField(pk=True)
    username = fields.Char<PERSON>ield(max_length=50, unique=True, description="用户名")
    email = fields.CharField(max_length=100, unique=True, description="邮箱")
    password = fields.CharField(max_length=255, description="密码")
    nickname = fields.CharField(max_length=50, null=True, description="昵称")
    avatar = fields.CharField(max_length=255, null=True, description="头像")
    phone = fields.Char<PERSON><PERSON>(max_length=20, null=True, description="手机号")
    is_active = fields.BooleanField(default=True, description="是否激活")
    is_superuser = fields.BooleanField(default=False, description="是否超级用户")
    last_login = fields.DatetimeField(null=True, description="最后登录时间")
    dept_id = fields.IntField(null=True, description="部门ID")
    
    # 多对多关系
    roles: fields.ManyToManyRelation["Role"] = fields.ManyToManyField(
        "models.Role", related_name="users", through="user_role"
    )

    class Meta:
        table = "admin_users"
        table_description = "用户表"

    def __str__(self):
        return self.username


class Role(Model, TimestampMixin):
    """角色模型"""
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, unique=True, description="角色名称")
    description = fields.CharField(max_length=255, null=True, description="角色描述")
    is_active = fields.BooleanField(default=True, description="是否激活")
    
    # 多对多关系
    menus: fields.ManyToManyRelation["Menu"] = fields.ManyToManyField(
        "models.Menu", related_name="roles", through="role_menu"
    )
    apis: fields.ManyToManyRelation["Api"] = fields.ManyToManyField(
        "models.Api", related_name="roles", through="role_api"
    )

    class Meta:
        table = "admin_roles"
        table_description = "角色表"

    def __str__(self):
        return self.name


class Menu(Model, TimestampMixin):
    """菜单模型"""
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, description="菜单名称")
    menu_type = fields.CharEnumField(MenuType, default=MenuType.CATALOG, description="菜单类型")
    icon = fields.CharField(max_length=100, null=True, description="菜单图标")
    path = fields.CharField(max_length=100, description="菜单路径")
    order = fields.IntField(default=0, description="排序")
    parent_id = fields.IntField(default=0, description="父菜单ID")
    is_hidden = fields.BooleanField(default=False, description="是否隐藏")
    component = fields.CharField(max_length=100, default="Layout", description="组件")
    keepalive = fields.BooleanField(default=True, description="是否缓存")
    redirect = fields.CharField(max_length=100, null=True, description="重定向")

    class Meta:
        table = "admin_menus"
        table_description = "菜单表"

    def __str__(self):
        return self.name


class Api(Model, TimestampMixin):
    """API模型"""
    id = fields.IntField(pk=True)
    path = fields.CharField(max_length=255, description="API路径")
    method = fields.CharField(max_length=10, description="请求方法")
    summary = fields.CharField(max_length=255, null=True, description="API描述")
    tags = fields.CharField(max_length=100, null=True, description="API标签")

    class Meta:
        table = "admin_apis"
        table_description = "API表"
        unique_together = (("path", "method"),)

    def __str__(self):
        return f"{self.method} {self.path}"


class Dept(Model, TimestampMixin):
    """部门模型"""
    id = fields.IntField(pk=True)
    name = fields.CharField(max_length=50, description="部门名称")
    description = fields.CharField(max_length=255, null=True, description="部门描述")
    parent_id = fields.IntField(default=0, description="父部门ID")
    order = fields.IntField(default=0, description="排序")
    is_active = fields.BooleanField(default=True, description="是否激活")

    class Meta:
        table = "admin_depts"
        table_description = "部门表"

    def __str__(self):
        return self.name


class AuditLog(Model, TimestampMixin):
    """审计日志模型"""
    id = fields.IntField(pk=True)
    user_id = fields.IntField(null=True, description="用户ID")
    username = fields.CharField(max_length=50, null=True, description="用户名")
    method = fields.CharField(max_length=10, description="请求方法")
    path = fields.CharField(max_length=255, description="请求路径")
    ip = fields.CharField(max_length=50, null=True, description="IP地址")
    user_agent = fields.CharField(max_length=500, null=True, description="用户代理")
    request_data = fields.JSONField(null=True, description="请求数据")
    response_data = fields.JSONField(null=True, description="响应数据")
    status_code = fields.IntField(null=True, description="状态码")
    duration = fields.FloatField(null=True, description="耗时(秒)")

    class Meta:
        table = "admin_audit_logs"
        table_description = "审计日志表"

    def __str__(self):
        return f"{self.method} {self.path}"
