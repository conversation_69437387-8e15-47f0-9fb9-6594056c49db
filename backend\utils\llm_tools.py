import re
from urllib.parse import urlparse, parse_qs, unquote

def get_tag_url(text: str) -> dict:
    # 匹配Markdown图片格式: ![描述](URL)
    markdown_image_pattern = r'!\[(.*?)\]\((.*?)\)'
    # 匹配普通Markdown链接格式: [描述](URL)
    markdown_link_pattern = r'\[(.*?)\]\((.*?)\)'
    
    # 1. 优先尝试匹配Markdown图片格式
    image_match = re.search(markdown_image_pattern, text)
    if image_match:
        title = image_match.group(1)
        link = image_match.group(2)
        is_markdown = True
    else:
        # 2. 尝试匹配普通Markdown链接格式
        link_match = re.search(markdown_link_pattern, text)
        if link_match:
            title = link_match.group(1)
            link = link_match.group(2)
            is_markdown = False
        else:
            # 3. 最后尝试匹配纯URL格式
            url_pattern = r'(https?://[^\s<]+[^)\s\.,!<])'  # 改进：排除结尾标点
            url_match = re.search(url_pattern, text)
            if url_match:
                link = url_match.group(1)
                title = ""
                is_markdown = False
            else:
                return {"title": "", "link": ""}
    
    # 处理特殊参数
    if '&version_id=null' in link:
        link = link.split('&version_id=null')[0]
    
    # 修复URL结尾问题
    link = link.rstrip('?&')  # 移除结尾的?和&
    
    # 尝试从URL参数/路径中提取标题（仅当无描述时）
    if not title:
        if '&prefix=' in link:
            try:
                parsed = urlparse(link)
                query = parse_qs(parsed.query)
                if 'prefix' in query:
                    title = unquote(query['prefix'][0])
                    if '.' in title:
                        title = title.split('.', 1)[0]
            except Exception:
                pass
        elif '.' in link.split('/')[-1]:
            filename = link.split('/')[-1].split('?')[0]
            title = filename.rsplit('.', 1)[0] if '.' in filename else filename
    
    return {"title": title, "link": link}