以下是为你的实时语音处理项目精选的日志用 **Emoji 符号分类方案**，按模块和场景分类，提升日志可读性和调试效率：

---

### **核心处理流程**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| 🎵     | 音频文件相关操作              | `🎵 接收音频文件: test.wav`    |
| 🎙️    | 语音识别(STT)阶段            | `🎙️ 开始STT处理 (12s)`        |
| 💬     | 文本生成(LLM)阶段            | `💬 LLM生成回答 [耗时: 3.2s]`  | 
| 🔊     | 语音合成(TTS)阶段            | `🔊 TTS生成语音 URL: ...`      |
| 🤖     | AI模型调用                  | `🤖 调用GPT-4 API`             |

---

### **状态标识**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| 🚀     | 开始处理请求                 | `🚀 收到新请求 /audio/text`    |
| ✅     | 成功状态                   | `✅ STT处理完成`               |
| ⚠️     | 警告信息                   | `⚠️ 音频长度接近限制`           |
| ❌     | 错误/失败状态               | `❌ 文件大小超限 (18MB)`        |
| 🛑     | 关键错误                   | `🛑 STT服务不可用`              |

---

### **缓存相关**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| ⚡     | 缓存命中                   | `⚡ STT缓存命中 [哈希: a1b2c3]` |
| 💾     | 写入缓存                   | `💾 缓存LLM结果 (TTL: 10m)`    |
| 🔄     | 缓存刷新                   | `🔄 强制刷新TTS缓存`            |

---

### **网络与性能**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| 🌐     | 外部API调用                | `🌐 调用OpenAI STT API`        |
| ⏳     | 处理延迟提示               | `⏳ LLM响应延迟 +2.3s`         |
| 🚄     | 快速处理                   | `🚄 总耗时 1.8s (高速模式)`    |

---

### **数据流跟踪**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| 📥     | 接收输入数据               | `📥 接收音频大小: 4.2MB`       |
| 📤     | 输出结果                   | `📤 返回结果: {text: ...}`     |
| 🔗     | 数据关联                   | `🔗 关联请求ID: 7f8g9h`        |

---

### **安全与校验**
| Emoji | 适用场景                     | 示例                         |
|-------|----------------------------|-----------------------------|
| 🔒     | 安全校验通过               | `🔒 用户认证通过`              |
| 🚫     | 非法请求拦截               | `🚫 非法文件类型拦截`           |

---

### **推荐组合使用示例**
```python
# 典型日志消息
logger.info(f"🎵📥 接收音频文件: {file_name} (大小: {len(file_content)/1024:.1f}KB)")
logger.warning(f"⚠️🎙️ STT处理延迟: +{delay}s 超过阈值")
logger.error(f"❌🛑 TTS服务异常: {error_msg}")
logger.debug(f"⚡💬 LLM缓存命中 [问题哈希: {question_hash[:6]}]")
```

---

### **进阶技巧**
1. **颜色组合** (需终端支持):
   ```python
   # 使用 \033[ 实现颜色+Emoji (示例)
   print(f"\033[32m✅\033[0m 成功生成回答")  # 绿色成功标记
   print(f"\033[31m❌\033[0m 错误: {error}") # 红色错误标记
   ```

2. **前后端统一符号**:
   ```python
   # 在返回给前端的JSON中添加状态符号
   return {
       "status": "⏳ 处理中", 
       "detail": "💬 LLM生成中..."
   }
   ```

---

选择 **2-3 个核心符号** 保持一致性即可，过多会影响日志可读性。需要我帮你设计某个具体模块的符号方案吗？ 😊