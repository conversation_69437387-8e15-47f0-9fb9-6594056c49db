<template>
  <div class="environment-container">
    <n-card title="环境配置" :bordered="false" size="small" class="h-full shadow-sm rounded-16px">
      <n-alert type="info" class="mb-4">
        这里可以配置数字人系统的环境参数，如API密钥、服务地址等。
      </n-alert>
      
      <n-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        label-placement="left"
        :label-width="120"
        class="max-w-600px"
      >
        <n-form-item label="API基础地址" path="baseUrl">
          <n-input v-model:value="formData.baseUrl" placeholder="请输入API基础地址" />
        </n-form-item>
        
        <n-form-item label="API密钥" path="apiKey">
          <n-input
            v-model:value="formData.apiKey"
            type="password"
            show-password-on="click"
            placeholder="请输入API密钥"
          />
        </n-form-item>
        
        <n-form-item label="TTS服务地址" path="ttsUrl">
          <n-input v-model:value="formData.ttsUrl" placeholder="请输入TTS服务地址" />
        </n-form-item>
        
        <n-form-item label="语音名称" path="voiceName">
          <n-input v-model:value="formData.voiceName" placeholder="请输入语音名称" />
        </n-form-item>
        
        <n-form-item label="日志级别" path="logLevel">
          <n-select
            v-model:value="formData.logLevel"
            :options="logLevelOptions"
            placeholder="请选择日志级别"
          />
        </n-form-item>
        
        <n-form-item label="运行模式" path="mode">
          <n-select
            v-model:value="formData.mode"
            :options="modeOptions"
            placeholder="请选择运行模式"
          />
        </n-form-item>
        
        <n-form-item>
          <n-space>
            <n-button type="primary" @click="handleSave">
              <template #icon>
                <i class="i-material-symbols:save" />
              </template>
              保存配置
            </n-button>
            <n-button @click="handleReset">
              <template #icon>
                <i class="i-material-symbols:refresh" />
              </template>
              重置
            </n-button>
          </n-space>
        </n-form-item>
      </n-form>
    </n-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useMessage } from 'naive-ui'

const message = useMessage()

// 表单数据
const formData = reactive({
  baseUrl: 'http://localhost:8288',
  apiKey: '',
  ttsUrl: '',
  voiceName: '',
  logLevel: 'INFO',
  mode: 'development'
})

// 表单验证规则
const rules = {
  baseUrl: [
    { required: true, message: '请输入API基础地址', trigger: 'blur' }
  ],
  apiKey: [
    { required: true, message: '请输入API密钥', trigger: 'blur' }
  ]
}

// 选项数据
const logLevelOptions = [
  { label: 'DEBUG', value: 'DEBUG' },
  { label: 'INFO', value: 'INFO' },
  { label: 'WARNING', value: 'WARNING' },
  { label: 'ERROR', value: 'ERROR' }
]

const modeOptions = [
  { label: '开发模式', value: 'development' },
  { label: '生产模式', value: 'production' },
  { label: '测试模式', value: 'test' }
]

const formRef = ref()

// 保存配置
const handleSave = () => {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // 这里可以调用API保存配置
      message.success('配置保存成功')
    } else {
      message.error('请检查表单数据')
    }
  })
}

// 重置表单
const handleReset = () => {
  Object.assign(formData, {
    baseUrl: 'http://localhost:8288',
    apiKey: '',
    ttsUrl: '',
    voiceName: '',
    logLevel: 'INFO',
    mode: 'development'
  })
  message.info('表单已重置')
}

// 初始化
onMounted(() => {
  // 这里可以加载现有配置
})
</script>

<style scoped>
.environment-container {
  padding: 16px;
}
</style>
