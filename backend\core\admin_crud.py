"""
管理系统CRUD基础类
"""
from typing import Any, Dict, Generic, List, Optional, Type, TypeVar, Union
from pydantic import BaseModel
from tortoise.models import Model
from tortoise.queryset import QuerySet
from tortoise.expressions import Q

ModelType = TypeVar("ModelType", bound=Model)
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)


class CRUDBase(Generic[ModelType, CreateSchemaType, UpdateSchemaType]):
    def __init__(self, model: Type[ModelType]):
        """
        CRUD object with default methods to Create, Read, Update, Delete (CRUD).
        **Parameters**
        * `model`: A Tortoise ORM model class
        * `schema`: A Pydantic model (schema) class
        """
        self.model = model

    async def get(self, id: Any) -> Optional[ModelType]:
        """根据ID获取单个对象"""
        return await self.model.get_or_none(id=id)

    async def get_multi(
        self, *, skip: int = 0, limit: int = 100
    ) -> List[ModelType]:
        """获取多个对象"""
        return await self.model.all().offset(skip).limit(limit)

    async def create(self, *, obj_in: CreateSchemaType) -> ModelType:
        """创建对象"""
        obj_in_data = obj_in.model_dump()
        db_obj = await self.model.create(**obj_in_data)
        return db_obj

    async def update(
        self,
        *,
        db_obj: ModelType,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> ModelType:
        """更新对象"""
        if isinstance(obj_in, dict):
            update_data = obj_in
        else:
            update_data = obj_in.model_dump(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(db_obj, field, value)
        
        await db_obj.save()
        return db_obj

    async def update_by_id(
        self,
        *,
        id: Any,
        obj_in: Union[UpdateSchemaType, Dict[str, Any]]
    ) -> Optional[ModelType]:
        """根据ID更新对象"""
        db_obj = await self.get(id=id)
        if db_obj:
            return await self.update(db_obj=db_obj, obj_in=obj_in)
        return None

    async def remove(self, *, id: Any) -> Optional[ModelType]:
        """删除对象"""
        obj = await self.model.get_or_none(id=id)
        if obj:
            await obj.delete()
        return obj

    async def list(
        self,
        *,
        page: int = 1,
        page_size: int = 10,
        search: Optional[Q] = None,
        order: Optional[List[str]] = None
    ) -> tuple[int, List[ModelType]]:
        """分页查询"""
        queryset = self.model.all()
        
        if search:
            queryset = queryset.filter(search)
        
        if order:
            queryset = queryset.order_by(*order)
        
        total = await queryset.count()
        
        offset = (page - 1) * page_size
        items = await queryset.offset(offset).limit(page_size)
        
        return total, items

    async def exists(self, **kwargs) -> bool:
        """检查对象是否存在"""
        return await self.model.filter(**kwargs).exists()

    async def count(self, search: Optional[Q] = None) -> int:
        """计数"""
        queryset = self.model.all()
        if search:
            queryset = queryset.filter(search)
        return await queryset.count()

    async def get_by_field(self, field: str, value: Any) -> Optional[ModelType]:
        """根据字段获取对象"""
        return await self.model.filter(**{field: value}).first()

    async def get_multi_by_field(
        self, field: str, value: Any, limit: int = 100
    ) -> List[ModelType]:
        """根据字段获取多个对象"""
        return await self.model.filter(**{field: value}).limit(limit)

    async def bulk_create(self, objs_in: List[CreateSchemaType]) -> List[ModelType]:
        """批量创建"""
        objs_data = [obj.model_dump() for obj in objs_in]
        return await self.model.bulk_create([self.model(**data) for data in objs_data])

    async def bulk_update(
        self, 
        objs: List[ModelType], 
        fields: List[str]
    ) -> None:
        """批量更新"""
        await self.model.bulk_update(objs, fields)

    async def get_or_create(
        self, 
        defaults: Optional[Dict[str, Any]] = None, 
        **kwargs
    ) -> tuple[ModelType, bool]:
        """获取或创建对象"""
        obj = await self.model.filter(**kwargs).first()
        if obj:
            return obj, False
        
        create_data = kwargs.copy()
        if defaults:
            create_data.update(defaults)
        
        obj = await self.model.create(**create_data)
        return obj, True
