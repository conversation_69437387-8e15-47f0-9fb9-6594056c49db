"""
管理系统认证授权模块
"""
import jwt
from datetime import datetime, timedelta, timezone
from typing import Optional
from fastapi import HTTP<PERSON>x<PERSON>, Header, Depends, Request
from fastapi.security import H<PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from passlib.context import Crypt<PERSON>ontext

from settings.config import settings
from core.admin_models import User, Role, Api
from core.logger import logger

# 密码加密上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT 配置
SECRET_KEY = getattr(settings, 'SECRET_KEY', 'your-secret-key-here')
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = getattr(settings, 'ACCESS_TOKEN_EXPIRE_MINUTES', 30)

security = HTTPBearer()


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """验证密码"""
    return pwd_context.verify(plain_password, hashed_password)


def get_password_hash(password: str) -> str:
    """获取密码哈希"""
    return pwd_context.hash(password)


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """创建访问令牌"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)) -> User:
    """获取当前用户"""
    token = credentials.credentials
    
    try:
        if token == "dev":
            # 开发模式
            user = await User.filter().first()
            if not user:
                raise HTTPException(status_code=401, detail="No users found")
            return user
        
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id: int = payload.get("user_id")
        if user_id is None:
            raise HTTPException(status_code=401, detail="Invalid token")
            
    except jwt.ExpiredSignatureError:
        raise HTTPException(status_code=401, detail="Token expired")
    except jwt.JWTError:
        raise HTTPException(status_code=401, detail="Invalid token")
    
    user = await User.filter(id=user_id).first()
    if user is None:
        raise HTTPException(status_code=401, detail="User not found")
    
    if not user.is_active:
        raise HTTPException(status_code=401, detail="User is inactive")
    
    return user


async def check_permission(request: Request, current_user: User = Depends(get_current_user)) -> None:
    """检查权限"""
    # 超级用户跳过权限检查
    if current_user.is_superuser:
        return
    
    method = request.method
    path = request.url.path
    
    # 获取用户角色
    roles = await current_user.roles.all()
    if not roles:
        raise HTTPException(status_code=403, detail="User has no roles assigned")
    
    # 获取角色对应的API权限
    permission_apis = []
    for role in roles:
        apis = await role.apis.all()
        permission_apis.extend([(api.method, api.path) for api in apis])
    
    # 去重
    permission_apis = list(set(permission_apis))
    
    # 检查权限
    if (method, path) not in permission_apis:
        logger.warning(f"Permission denied: {current_user.username} tried to access {method} {path}")
        raise HTTPException(
            status_code=403, 
            detail=f"Permission denied for {method} {path}"
        )


class AuthControl:
    """认证控制类"""
    
    @classmethod
    async def is_authed(cls, token: str = Header(..., description="token验证")) -> Optional[User]:
        """验证用户是否已认证"""
        try:
            if token == "dev":
                user = await User.filter().first()
                if not user:
                    raise HTTPException(status_code=401, detail="No users found")
                return user
            
            decode_data = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id = decode_data.get("user_id")
            
            user = await User.filter(id=user_id).first()
            if not user:
                raise HTTPException(status_code=401, detail="Authentication failed")
            
            return user
            
        except jwt.DecodeError:
            raise HTTPException(status_code=401, detail="Invalid token")
        except jwt.ExpiredSignatureError:
            raise HTTPException(status_code=401, detail="Token expired")
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(status_code=500, detail=f"Authentication error: {str(e)}")


class PermissionControl:
    """权限控制类"""
    
    @classmethod
    async def has_permission(cls, request: Request, current_user: User = Depends(AuthControl.is_authed)) -> None:
        """检查用户是否有权限"""
        if current_user.is_superuser:
            return
        
        method = request.method
        path = request.url.path
        
        roles = await current_user.roles.all()
        if not roles:
            raise HTTPException(status_code=403, detail="The user is not bound to a role")
        
        # 获取所有角色的API权限
        apis = []
        for role in roles:
            role_apis = await role.apis.all()
            apis.extend(role_apis)
        
        permission_apis = list(set((api.method, api.path) for api in apis))
        
        if (method, path) not in permission_apis:
            raise HTTPException(
                status_code=403, 
                detail=f"Permission denied method:{method} path:{path}"
            )


# 依赖项
DependAuth = Depends(get_current_user)
DependPermission = Depends(check_permission)
