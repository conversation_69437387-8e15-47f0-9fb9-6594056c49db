#!/usr/bin/env python3
"""
启动管理系统的脚本
"""
import uvicorn
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动数字人项目管理系统...")
    print("📍 后端地址: http://127.0.0.1:8288")
    print("📍 API文档: http://127.0.0.1:8288/")
    print("📍 管理接口: http://127.0.0.1:8288/admin/")
    print("=" * 50)
    
    uvicorn.run(
        "app:app",
        host="127.0.0.1",
        port=8288,
        reload=True,
        log_level="info"
    )
