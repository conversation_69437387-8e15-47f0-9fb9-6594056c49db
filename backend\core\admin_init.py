"""
管理系统初始化脚本
"""
from core.admin_models import User, <PERSON>, <PERSON>u, Api, Dept
from core.admin_auth import get_password_hash
from core.logger import logger


async def init_admin_data():
    """初始化管理系统基础数据"""
    try:
        # 初始化超级管理员
        await init_superuser()
        
        # 初始化基础菜单
        await init_menus()
        
        # 初始化基础角色
        await init_roles()
        
        # 初始化部门
        await init_departments()
        
        logger.info("管理系统基础数据初始化完成")
        
    except Exception as e:
        logger.error(f"管理系统初始化失败: {e}")


async def init_superuser():
    """初始化超级管理员"""
    # 检查是否已存在超级管理员
    admin_exists = await User.filter(is_superuser=True).exists()
    if admin_exists:
        logger.info("超级管理员已存在，跳过创建")
        return
    
    # 创建超级管理员
    admin_user = await User.create(
        username="admin",
        email="<EMAIL>",
        password=get_password_hash("123456"),
        nickname="超级管理员",
        is_active=True,
        is_superuser=True
    )
    
    logger.info(f"超级管理员创建成功: {admin_user.username}")


async def init_menus():
    """初始化基础菜单"""
    menus_data = [
        {
            "name": "工作台",
            "path": "/workbench",
            "component": "Layout",
            "icon": "icon-park-outline:workbench",
            "order": 1,
            "parent_id": 0,
            "menu_type": "menu"
        },
        {
            "name": "数字人管理",
            "path": "/digital-human",
            "component": "Layout",
            "icon": "mdi:robot-outline",
            "order": 2,
            "parent_id": 0,
            "menu_type": "catalog"
        },
        {
            "name": "V2 API管理",
            "path": "/digital-human/v2-apis",
            "component": "/digital-human/v2-apis/index",
            "icon": "mdi:api",
            "order": 1,
            "parent_id": 0,  # 这里需要动态设置为数字人管理的ID
            "menu_type": "menu"
        },
        {
            "name": "环境配置",
            "path": "/digital-human/environment",
            "component": "/digital-human/environment/index",
            "icon": "mdi:cog-outline",
            "order": 2,
            "parent_id": 0,  # 这里需要动态设置为数字人管理的ID
            "menu_type": "menu"
        },
        {
            "name": "设备管理",
            "path": "/digital-human/devices",
            "component": "/digital-human/devices/index",
            "icon": "mdi:devices",
            "order": 3,
            "parent_id": 0,  # 这里需要动态设置为数字人管理的ID
            "menu_type": "menu"
        },
        {
            "name": "系统管理",
            "path": "/system",
            "component": "Layout",
            "icon": "mdi:cog",
            "order": 99,
            "parent_id": 0,
            "menu_type": "catalog"
        },
        {
            "name": "用户管理",
            "path": "/system/user",
            "component": "/system/user/index",
            "icon": "mdi:account-multiple",
            "order": 1,
            "parent_id": 0,  # 这里需要动态设置为系统管理的ID
            "menu_type": "menu"
        },
        {
            "name": "角色管理",
            "path": "/system/role",
            "component": "/system/role/index",
            "icon": "mdi:account-key",
            "order": 2,
            "parent_id": 0,  # 这里需要动态设置为系统管理的ID
            "menu_type": "menu"
        },
        {
            "name": "菜单管理",
            "path": "/system/menu",
            "component": "/system/menu/index",
            "icon": "mdi:menu",
            "order": 3,
            "parent_id": 0,  # 这里需要动态设置为系统管理的ID
            "menu_type": "menu"
        }
    ]
    
    # 检查菜单是否已存在
    menu_exists = await Menu.exists()
    if menu_exists:
        logger.info("菜单数据已存在，跳过创建")
        return
    
    # 创建父级菜单
    parent_menus = {}
    for menu_data in menus_data:
        if menu_data["parent_id"] == 0 and menu_data["menu_type"] == "catalog":
            menu = await Menu.create(**menu_data)
            parent_menus[menu_data["name"]] = menu.id
            logger.info(f"创建父级菜单: {menu.name}")
    
    # 创建子菜单
    for menu_data in menus_data:
        if menu_data["parent_id"] == 0 and menu_data["menu_type"] == "menu":
            # 根据路径判断父级菜单
            if menu_data["path"].startswith("/digital-human/"):
                menu_data["parent_id"] = parent_menus.get("数字人管理", 0)
            elif menu_data["path"].startswith("/system/"):
                menu_data["parent_id"] = parent_menus.get("系统管理", 0)
            
            menu = await Menu.create(**menu_data)
            logger.info(f"创建子菜单: {menu.name}")
        elif menu_data["parent_id"] == 0 and menu_data["menu_type"] == "menu":
            # 顶级菜单
            menu = await Menu.create(**menu_data)
            logger.info(f"创建顶级菜单: {menu.name}")


async def init_roles():
    """初始化基础角色"""
    roles_data = [
        {
            "name": "超级管理员",
            "description": "系统超级管理员，拥有所有权限",
            "is_active": True
        },
        {
            "name": "管理员",
            "description": "系统管理员，拥有大部分权限",
            "is_active": True
        },
        {
            "name": "普通用户",
            "description": "普通用户，拥有基础权限",
            "is_active": True
        }
    ]
    
    # 检查角色是否已存在
    role_exists = await Role.exists()
    if role_exists:
        logger.info("角色数据已存在，跳过创建")
        return
    
    for role_data in roles_data:
        role = await Role.create(**role_data)
        logger.info(f"创建角色: {role.name}")
        
        # 为超级管理员角色分配所有菜单权限
        if role.name == "超级管理员":
            all_menus = await Menu.all()
            await role.menus.add(*all_menus)
            logger.info(f"为角色 {role.name} 分配了所有菜单权限")


async def init_departments():
    """初始化部门"""
    depts_data = [
        {
            "name": "总公司",
            "description": "总公司",
            "parent_id": 0,
            "order": 1,
            "is_active": True
        },
        {
            "name": "技术部",
            "description": "技术开发部门",
            "parent_id": 0,  # 这里需要动态设置为总公司的ID
            "order": 1,
            "is_active": True
        },
        {
            "name": "产品部",
            "description": "产品管理部门",
            "parent_id": 0,  # 这里需要动态设置为总公司的ID
            "order": 2,
            "is_active": True
        }
    ]
    
    # 检查部门是否已存在
    dept_exists = await Dept.exists()
    if dept_exists:
        logger.info("部门数据已存在，跳过创建")
        return
    
    # 创建总公司
    company = None
    for dept_data in depts_data:
        if dept_data["parent_id"] == 0:
            dept = await Dept.create(**dept_data)
            if dept.name == "总公司":
                company = dept
            logger.info(f"创建部门: {dept.name}")
    
    # 创建子部门
    if company:
        for dept_data in depts_data:
            if dept_data["parent_id"] == 0 and dept_data["name"] != "总公司":
                dept_data["parent_id"] = company.id
                dept = await Dept.create(**dept_data)
                logger.info(f"创建子部门: {dept.name}")


async def init_apis():
    """初始化API权限数据"""
    # 这个函数可以在应用启动时调用，自动扫描所有API路由并创建权限记录
    pass
