#!/usr/bin/env python3
"""
测试认证系统
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_auth():
    try:
        from core.admin_auth import verify_password, get_password_hash
        
        print("🔍 测试密码加密和验证...")
        
        # 测试密码
        password = "test123"
        
        # 加密密码
        hashed = get_password_hash(password)
        print(f"✅ 密码加密成功: {password} -> {hashed[:50]}...")
        
        # 验证密码
        is_valid = verify_password(password, hashed)
        print(f"✅ 密码验证: {is_valid}")
        
        # 测试错误密码
        is_invalid = verify_password("wrong_password", hashed)
        print(f"✅ 错误密码验证: {is_invalid}")
        
        if is_valid and not is_invalid:
            print("🎉 认证系统测试通过！")
            return True
        else:
            print("❌ 认证系统测试失败！")
            return False
            
    except Exception as e:
        print(f"❌ 认证系统测试异常: {e}")
        return False

if __name__ == "__main__":
    success = test_auth()
    sys.exit(0 if success else 1)
