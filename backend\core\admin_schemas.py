"""
管理系统数据模式
"""
from pydantic import BaseModel, Field, EmailStr
from typing import Optional, List
from datetime import datetime
from enum import Enum


class MenuType(str, Enum):
    CATALOG = "catalog"
    MENU = "menu"
    BUTTON = "button"


# 基础响应模式
class BaseResponse(BaseModel):
    code: int = 200
    msg: str = "Success"


class Success(BaseResponse):
    data: Optional[dict] = None


class SuccessExtra(BaseResponse):
    data: Optional[List] = None
    total: int = 0
    page: int = 1
    page_size: int = 10


class Fail(BaseResponse):
    code: int = 400
    msg: str = "Fail"


# 登录相关
class CredentialsSchema(BaseModel):
    username: str = Field(..., description="用户名")
    password: str = Field(..., description="密码")


class JWTPayload(BaseModel):
    user_id: int
    username: str
    is_superuser: bool
    exp: datetime


class JWTOut(BaseModel):
    access_token: str
    username: str


# 用户相关
class UserBase(BaseModel):
    username: str = Field(..., max_length=50, description="用户名")
    email: EmailStr = Field(..., description="邮箱")
    nickname: Optional[str] = Field(None, max_length=50, description="昵称")
    avatar: Optional[str] = Field(None, description="头像")
    phone: Optional[str] = Field(None, max_length=20, description="手机号")
    is_active: bool = Field(True, description="是否激活")
    is_superuser: bool = Field(False, description="是否超级用户")
    dept_id: Optional[int] = Field(None, description="部门ID")


class UserCreate(UserBase):
    password: str = Field(..., min_length=6, description="密码")
    role_ids: List[int] = Field([], description="角色ID列表")


class UserUpdate(UserBase):
    id: int = Field(..., description="用户ID")
    role_ids: List[int] = Field([], description="角色ID列表")


class UserOut(UserBase):
    id: int
    created_at: datetime
    updated_at: datetime
    last_login: Optional[datetime] = None


class UpdatePassword(BaseModel):
    old_password: str = Field(..., description="旧密码")
    new_password: str = Field(..., min_length=6, description="新密码")


# 角色相关
class RoleBase(BaseModel):
    name: str = Field(..., max_length=50, description="角色名称")
    description: Optional[str] = Field(None, max_length=255, description="角色描述")
    is_active: bool = Field(True, description="是否激活")


class RoleCreate(RoleBase):
    menu_ids: List[int] = Field([], description="菜单ID列表")
    api_infos: List[dict] = Field([], description="API信息列表")


class RoleUpdate(RoleBase):
    id: int = Field(..., description="角色ID")
    menu_ids: List[int] = Field([], description="菜单ID列表")
    api_infos: List[dict] = Field([], description="API信息列表")


class RoleOut(RoleBase):
    id: int
    created_at: datetime
    updated_at: datetime


# 菜单相关
class MenuBase(BaseModel):
    name: str = Field(..., max_length=50, description="菜单名称")
    menu_type: MenuType = Field(MenuType.CATALOG, description="菜单类型")
    icon: Optional[str] = Field(None, description="菜单图标")
    path: str = Field(..., description="菜单路径")
    order: int = Field(0, description="排序")
    parent_id: int = Field(0, description="父菜单ID")
    is_hidden: bool = Field(False, description="是否隐藏")
    component: str = Field("Layout", description="组件")
    keepalive: bool = Field(True, description="是否缓存")
    redirect: Optional[str] = Field(None, description="重定向")


class MenuCreate(MenuBase):
    pass


class MenuUpdate(MenuBase):
    id: int = Field(..., description="菜单ID")


class MenuOut(MenuBase):
    id: int
    created_at: datetime
    updated_at: datetime


# API相关
class ApiBase(BaseModel):
    path: str = Field(..., description="API路径")
    method: str = Field(..., description="请求方法")
    summary: Optional[str] = Field(None, description="API描述")
    tags: Optional[str] = Field(None, description="API标签")


class ApiCreate(ApiBase):
    pass


class ApiUpdate(ApiBase):
    id: int = Field(..., description="API ID")


class ApiOut(ApiBase):
    id: int
    created_at: datetime
    updated_at: datetime


# 部门相关
class DeptBase(BaseModel):
    name: str = Field(..., max_length=50, description="部门名称")
    description: Optional[str] = Field(None, max_length=255, description="部门描述")
    parent_id: int = Field(0, description="父部门ID")
    order: int = Field(0, description="排序")
    is_active: bool = Field(True, description="是否激活")


class DeptCreate(DeptBase):
    pass


class DeptUpdate(DeptBase):
    id: int = Field(..., description="部门ID")


class DeptOut(DeptBase):
    id: int
    created_at: datetime
    updated_at: datetime


# V2 API管理相关
class V2ApiInfo(BaseModel):
    path: str
    method: str
    summary: Optional[str] = None
    tags: Optional[str] = None
    enabled: bool = True


class V2ApiStats(BaseModel):
    total: int
    enabled: int
    disabled: int


class V2ApiToggle(BaseModel):
    path: str
    method: str
    enabled: bool
