<template>
  <div class="devices-container">
    <n-card title="设备管理" :bordered="false" size="small" class="h-full shadow-sm rounded-16px">
      <n-alert type="info" class="mb-4">
        这里可以管理数字人系统中的设备信息，包括设备注册、状态监控等。
      </n-alert>
      
      <!-- 查询表单 -->
      <n-form ref="queryFormRef" inline :label-width="80" :model="queryForm" class="mb-4">
        <n-form-item label="设备名称" path="name">
          <n-input v-model:value="queryForm.name" placeholder="请输入设备名称" clearable />
        </n-form-item>
        <n-form-item label="设备状态" path="status">
          <n-select
            v-model:value="queryForm.status"
            placeholder="请选择设备状态"
            :options="statusOptions"
            clearable
          />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="handleQuery">
            <template #icon>
              <i class="i-material-symbols:search" />
            </template>
            查询
          </n-button>
          <n-button class="ml-2" @click="handleReset">
            <template #icon>
              <i class="i-material-symbols:refresh" />
            </template>
            重置
          </n-button>
          <n-button class="ml-2" type="primary" @click="handleAdd">
            <template #icon>
              <i class="i-material-symbols:add" />
            </template>
            新增设备
          </n-button>
        </n-form-item>
      </n-form>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => row.id"
        flex-height
        class="h-480px"
      />
    </n-card>

    <!-- 设备表单弹窗 -->
    <n-modal v-model:show="showModal" preset="dialog" title="设备信息" class="w-600px">
      <n-form
        ref="modalFormRef"
        :model="modalForm"
        :rules="modalRules"
        label-placement="left"
        :label-width="100"
      >
        <n-form-item label="设备名称" path="name">
          <n-input v-model:value="modalForm.name" placeholder="请输入设备名称" />
        </n-form-item>
        <n-form-item label="设备描述" path="description">
          <n-input
            v-model:value="modalForm.description"
            type="textarea"
            placeholder="请输入设备描述"
            :rows="3"
          />
        </n-form-item>
        <n-form-item label="设备类型" path="type">
          <n-select
            v-model:value="modalForm.type"
            placeholder="请选择设备类型"
            :options="typeOptions"
          />
        </n-form-item>
        <n-form-item label="IP地址" path="ip">
          <n-input v-model:value="modalForm.ip" placeholder="请输入IP地址" />
        </n-form-item>
        <n-form-item label="端口" path="port">
          <n-input-number v-model:value="modalForm.port" placeholder="请输入端口" class="w-full" />
        </n-form-item>
      </n-form>
      <template #action>
        <n-space>
          <n-button @click="showModal = false">取消</n-button>
          <n-button type="primary" @click="handleSave">确定</n-button>
        </n-space>
      </template>
    </n-modal>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { NButton, NTag, useMessage } from 'naive-ui'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const showModal = ref(false)

// 查询表单
const queryForm = reactive({
  name: '',
  status: ''
})

// 模态框表单
const modalForm = reactive({
  id: null,
  name: '',
  description: '',
  type: '',
  ip: '',
  port: null,
  status: 'online'
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page
    getTableData()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    getTableData()
  }
})

// 选项数据
const statusOptions = [
  { label: '在线', value: 'online' },
  { label: '离线', value: 'offline' },
  { label: '维护中', value: 'maintenance' }
]

const typeOptions = [
  { label: '数字人终端', value: 'terminal' },
  { label: '摄像头', value: 'camera' },
  { label: '麦克风', value: 'microphone' },
  { label: '扬声器', value: 'speaker' }
]

// 表单验证规则
const modalRules = {
  name: [{ required: true, message: '请输入设备名称', trigger: 'blur' }],
  type: [{ required: true, message: '请选择设备类型', trigger: 'change' }],
  ip: [{ required: true, message: '请输入IP地址', trigger: 'blur' }],
  port: [{ required: true, message: '请输入端口', trigger: 'blur' }]
}

// 表格列定义
const columns = [
  { title: 'ID', key: 'id', width: 80 },
  { title: '设备名称', key: 'name', width: 150 },
  { title: '设备描述', key: 'description', ellipsis: { tooltip: true } },
  { title: '设备类型', key: 'type', width: 120 },
  { title: 'IP地址', key: 'ip', width: 120 },
  { title: '端口', key: 'port', width: 80 },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render: (row) => {
      const statusMap = {
        online: { type: 'success', text: '在线' },
        offline: { type: 'error', text: '离线' },
        maintenance: { type: 'warning', text: '维护中' }
      }
      const status = statusMap[row.status] || { type: 'default', text: '未知' }
      return h(NTag, { type: status.type }, { default: () => status.text })
    }
  },
  { title: '创建时间', key: 'created_at', width: 160 },
  {
    title: '操作',
    key: 'actions',
    width: 150,
    render: (row) => h('div', [
      h(
        NButton,
        {
          size: 'small',
          type: 'primary',
          class: 'mr-2',
          onClick: () => handleEdit(row)
        },
        { default: () => '编辑' }
      ),
      h(
        NButton,
        {
          size: 'small',
          type: 'error',
          onClick: () => handleDelete(row)
        },
        { default: () => '删除' }
      )
    ])
  }
]

const modalFormRef = ref()

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    // 模拟数据
    const mockData = [
      {
        id: 1,
        name: '数字人终端-001',
        description: '大厅数字人显示终端',
        type: 'terminal',
        ip: '*************',
        port: 8080,
        status: 'online',
        created_at: '2024-01-01 10:00:00'
      },
      {
        id: 2,
        name: '摄像头-001',
        description: '前台监控摄像头',
        type: 'camera',
        ip: '*************',
        port: 554,
        status: 'online',
        created_at: '2024-01-01 10:30:00'
      },
      {
        id: 3,
        name: '麦克风-001',
        description: '语音采集设备',
        type: 'microphone',
        ip: '*************',
        port: 8081,
        status: 'offline',
        created_at: '2024-01-01 11:00:00'
      }
    ]
    
    // 过滤数据
    let filteredData = mockData
    if (queryForm.name) {
      filteredData = filteredData.filter(item => 
        item.name.toLowerCase().includes(queryForm.name.toLowerCase())
      )
    }
    if (queryForm.status) {
      filteredData = filteredData.filter(item => item.status === queryForm.status)
    }
    
    tableData.value = filteredData
    pagination.itemCount = filteredData.length
  } catch (error) {
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 查询
const handleQuery = () => {
  pagination.page = 1
  getTableData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, { name: '', status: '' })
  pagination.page = 1
  getTableData()
}

// 新增
const handleAdd = () => {
  Object.assign(modalForm, {
    id: null,
    name: '',
    description: '',
    type: '',
    ip: '',
    port: null,
    status: 'online'
  })
  showModal.value = true
}

// 编辑
const handleEdit = (row) => {
  Object.assign(modalForm, { ...row })
  showModal.value = true
}

// 删除
const handleDelete = (row) => {
  // 这里可以调用删除API
  message.success(`设备 ${row.name} 删除成功`)
  getTableData()
}

// 保存
const handleSave = () => {
  modalFormRef.value?.validate((errors) => {
    if (!errors) {
      // 这里可以调用保存API
      message.success(modalForm.id ? '设备更新成功' : '设备创建成功')
      showModal.value = false
      getTableData()
    } else {
      message.error('请检查表单数据')
    }
  })
}

// 初始化
onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.devices-container {
  padding: 16px;
}
</style>
