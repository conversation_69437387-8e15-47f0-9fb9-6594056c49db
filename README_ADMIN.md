# 数字人项目管理系统

这是一个集成的数字人项目管理系统，将 vue-fastapi-admin 的前端管理界面与您现有的 backend 后端服务进行了整合。

## 系统架构

- **后端服务**: `backend/` - 基于 FastAPI 的统一后端服务
- **前端界面**: `vue-fastapi-admin/web/` - 基于 Vue3 + Naive UI 的管理界面
- **管理功能**: 集成了用户管理、角色管理、V2 API管理等功能

## 功能特性

### 🎯 V2 API管理
- 自动发现和展示 V2 API 接口
- API 状态监控和统计
- API 详细信息查看
- API 启用/禁用控制

### 🔧 环境配置
- 系统环境参数配置
- API 密钥管理
- 服务地址配置

### 📱 设备管理
- 数字人设备注册
- 设备状态监控
- 设备信息管理

### 👥 用户权限管理
- 用户账号管理
- 角色权限分配
- 菜单权限控制

## 快速开始

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend

# 激活虚拟环境
.venv\Scripts\activate  # Windows
# 或
source .venv/bin/activate  # Linux/Mac

# 安装依赖（如果还没安装）
pip install -r requirements.txt

# 启动后端服务
python start_admin.py
```

后端服务将在 `http://127.0.0.1:8288` 启动

### 2. 启动前端界面

```bash
# 进入前端目录
cd vue-fastapi-admin/web

# 安装依赖（如果还没安装）
pnpm install

# 启动前端服务
pnpm run dev
# 或者直接运行
start_frontend.bat
```

前端界面将在 `http://localhost:3100` 启动

### 3. 登录系统

- 访问: `http://localhost:3100`
- 默认账号: `admin`
- 默认密码: `123456`

## 目录结构

```
├── backend/                          # 后端服务
│   ├── api_versions/
│   │   ├── admin/                    # 管理系统API
│   │   │   ├── v2_management.py     # V2 API管理接口
│   │   │   └── routers.py           # 管理路由
│   │   ├── v2/                      # V2 业务API
│   │   └── ...
│   ├── core/                        # 核心模块
│   │   ├── admin_models.py          # 管理系统数据模型
│   │   ├── admin_schemas.py         # 管理系统数据模式
│   │   ├── admin_auth.py            # 认证授权
│   │   ├── admin_controllers.py     # 控制器
│   │   ├── admin_crud.py            # CRUD基础类
│   │   └── admin_init.py            # 初始化脚本
│   ├── app/
│   │   ├── factory.py               # 应用工厂（已集成管理路由）
│   │   └── lifespan.py              # 生命周期（已集成初始化）
│   └── start_admin.py               # 启动脚本
│
├── vue-fastapi-admin/web/            # 前端界面
│   ├── src/
│   │   ├── views/digital-human/     # 数字人管理页面
│   │   │   ├── v2-apis/            # V2 API管理
│   │   │   ├── environment/        # 环境配置
│   │   │   └── devices/            # 设备管理
│   │   └── api/index.js            # API接口（已更新）
│   ├── build/constant.js           # 构建配置（已更新代理）
│   └── start_frontend.bat          # 前端启动脚本
│
└── README_ADMIN.md                  # 本文档
```

## API 接口

### 管理系统 API

所有管理系统的 API 都在 `/admin` 路径下：

- `GET /admin/health` - 健康检查
- `GET /admin/info` - 系统信息
- `GET /admin/admin/v2-apis/list` - 获取V2 API列表
- `GET /admin/admin/v2-apis/stats` - 获取V2 API统计
- `GET /admin/admin/v2-apis/types` - 获取V2 API类型
- `GET /admin/admin/v2-apis/detail` - 获取V2 API详情
- `POST /admin/admin/v2-apis/toggle` - 切换V2 API状态
- `POST /admin/admin/v2-apis/refresh` - 刷新V2 API列表

### 业务 API

原有的业务 API 保持不变：

- `/v1/*` - V1 版本API
- `/v2/*` - V2 版本API
- `/utils/*` - 工具API

## 配置说明

### 后端配置

在 `backend/settings/config.py` 中添加了管理系统相关配置：

```python
# 管理系统配置
SECRET_KEY: str = "your-secret-key-here"
ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
JWT_ALGORITHM: str = "HS256"
```

### 前端配置

在 `vue-fastapi-admin/web/build/constant.js` 中配置了代理：

```javascript
'/api/v1': {
  target: 'http://127.0.0.1:8288',
  changeOrigin: true,
  rewrite: (path) => path.replace('/api/v1', '/admin'),
}
```

## 数据库

管理系统使用与业务系统相同的数据库，新增了以下表：

- `admin_users` - 管理用户表
- `admin_roles` - 角色表
- `admin_menus` - 菜单表
- `admin_apis` - API权限表
- `admin_depts` - 部门表
- `admin_audit_logs` - 审计日志表

## 开发说明

### 添加新的管理功能

1. 在 `backend/api_versions/admin/` 下创建新的路由文件
2. 在 `backend/api_versions/admin/routers.py` 中引入新路由
3. 在 `vue-fastapi-admin/web/src/views/` 下创建对应的前端页面
4. 在 `vue-fastapi-admin/web/src/api/index.js` 中添加API接口

### 自定义权限控制

可以在 `backend/core/admin_auth.py` 中修改权限控制逻辑，支持更细粒度的权限管理。

## 注意事项

1. 确保后端虚拟环境已正确配置
2. 确保前端已安装 pnpm 包管理器
3. 首次启动会自动初始化管理系统基础数据
4. 生产环境请修改默认密码和密钥配置

## 故障排除

### 后端启动失败
- 检查虚拟环境是否激活
- 检查依赖是否完整安装
- 检查数据库连接配置

### 前端启动失败
- 检查 Node.js 版本（建议 16+）
- 检查 pnpm 是否正确安装
- 清除缓存：`pnpm store prune`

### 接口调用失败
- 检查后端服务是否正常运行
- 检查代理配置是否正确
- 查看浏览器网络面板的错误信息

## 技术栈

- **后端**: FastAPI + Tortoise ORM + Redis
- **前端**: Vue3 + Vite + Naive UI + Pinia
- **数据库**: SQLite/MySQL/PostgreSQL（根据配置）
- **认证**: JWT Token

## 联系支持

如有问题，请检查：
1. 控制台错误日志
2. 网络请求状态
3. 数据库连接状态
4. 服务端口占用情况
