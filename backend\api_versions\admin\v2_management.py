"""
V2 API管理接口
"""
from fastapi import APIRouter, Query, HTTPException
from typing import List, Dict, Any
import inspect
from core.logger import logger
from core.admin_schemas import Success, SuccessExtra, Fail, V2ApiInfo, V2ApiStats

router = APIRouter()


def get_v2_api_info() -> List[Dict[str, Any]]:
    """获取V2 API信息"""
    try:
        # 导入V2路由模块
        from api_versions.v2.routers import router as v2_router
        
        apis = []
        for route in v2_router.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                for method in route.methods:
                    if method != 'HEAD':  # 排除HEAD方法
                        api_info = {
                            "path": route.path,
                            "method": method,
                            "summary": getattr(route, 'summary', None) or route.name,
                            "tags": list(getattr(route, 'tags', [])),
                            "enabled": True,  # 默认启用
                            "description": getattr(route, 'description', ''),
                        }
                        apis.append(api_info)
        
        return apis
    except Exception as e:
        logger.error(f"获取V2 API信息失败: {e}")
        return []


@router.get("/v2-apis/list", summary="获取V2 API列表")
async def get_v2_apis(
    page: int = Query(1, description="页码"),
    page_size: int = Query(10, description="每页数量"),
    path: str = Query("", description="API路径过滤"),
    method: str = Query("", description="请求方法过滤"),
    tags: str = Query("", description="标签过滤"),
):
    """获取V2 API列表"""
    try:
        apis = get_v2_api_info()
        
        # 过滤
        if path:
            apis = [api for api in apis if path.lower() in api["path"].lower()]
        if method:
            apis = [api for api in apis if method.upper() == api["method"].upper()]
        if tags:
            apis = [api for api in apis if any(tags.lower() in tag.lower() for tag in api["tags"])]
        
        # 分页
        total = len(apis)
        start = (page - 1) * page_size
        end = start + page_size
        paginated_apis = apis[start:end]
        
        return SuccessExtra(
            data=paginated_apis,
            total=total,
            page=page,
            page_size=page_size
        )
    except Exception as e:
        logger.error(f"获取V2 API列表失败: {e}")
        return Fail(code=500, msg="获取API列表失败")


@router.get("/v2-apis/stats", summary="获取V2 API统计")
async def get_v2_api_stats():
    """获取V2 API统计信息"""
    try:
        apis = get_v2_api_info()
        
        total = len(apis)
        enabled = sum(1 for api in apis if api.get("enabled", True))
        disabled = total - enabled
        
        stats = V2ApiStats(
            total=total,
            enabled=enabled,
            disabled=disabled
        )
        
        return Success(data=stats.model_dump())
    except Exception as e:
        logger.error(f"获取V2 API统计失败: {e}")
        return Fail(code=500, msg="获取API统计失败")


@router.get("/v2-apis/types", summary="获取V2 API类型")
async def get_v2_api_types():
    """获取V2 API的所有标签类型"""
    try:
        apis = get_v2_api_info()
        
        # 收集所有标签
        all_tags = set()
        for api in apis:
            all_tags.update(api.get("tags", []))
        
        # 收集所有方法
        all_methods = set()
        for api in apis:
            all_methods.add(api["method"])
        
        data = {
            "tags": sorted(list(all_tags)),
            "methods": sorted(list(all_methods))
        }
        
        return Success(data=data)
    except Exception as e:
        logger.error(f"获取V2 API类型失败: {e}")
        return Fail(code=500, msg="获取API类型失败")


@router.post("/v2-apis/toggle", summary="切换V2 API状态")
async def toggle_v2_api(
    path: str = Query(..., description="API路径"),
    method: str = Query(..., description="请求方法"),
    enabled: bool = Query(..., description="是否启用")
):
    """切换V2 API的启用状态"""
    try:
        # 这里可以实现API的启用/禁用逻辑
        # 由于当前架构限制，这里只是返回成功状态
        # 实际实现可能需要修改路由注册逻辑
        
        logger.info(f"切换API状态: {method} {path} -> {'启用' if enabled else '禁用'}")
        
        return Success(msg=f"API {method} {path} 已{'启用' if enabled else '禁用'}")
    except Exception as e:
        logger.error(f"切换V2 API状态失败: {e}")
        return Fail(code=500, msg="切换API状态失败")


@router.put("/v2-apis/update", summary="更新V2 API信息")
async def update_v2_api(
    path: str = Query(..., description="API路径"),
    method: str = Query(..., description="请求方法"),
    summary: str = Query("", description="API摘要"),
    description: str = Query("", description="API描述")
):
    """更新V2 API信息"""
    try:
        # 这里可以实现API信息的更新逻辑
        # 由于当前架构限制，这里只是返回成功状态
        
        logger.info(f"更新API信息: {method} {path}")
        
        return Success(msg=f"API {method} {path} 信息已更新")
    except Exception as e:
        logger.error(f"更新V2 API信息失败: {e}")
        return Fail(code=500, msg="更新API信息失败")


@router.post("/v2-apis/refresh", summary="刷新V2 API列表")
async def refresh_v2_apis():
    """刷新V2 API列表"""
    try:
        # 重新获取API信息
        apis = get_v2_api_info()
        
        logger.info(f"刷新V2 API列表完成，共 {len(apis)} 个API")
        
        return Success(
            data={"count": len(apis)},
            msg=f"刷新完成，共发现 {len(apis)} 个API"
        )
    except Exception as e:
        logger.error(f"刷新V2 API列表失败: {e}")
        return Fail(code=500, msg="刷新API列表失败")


@router.get("/v2-apis/detail", summary="获取V2 API详情")
async def get_v2_api_detail(
    path: str = Query(..., description="API路径"),
    method: str = Query(..., description="请求方法")
):
    """获取V2 API详细信息"""
    try:
        apis = get_v2_api_info()
        
        # 查找指定的API
        target_api = None
        for api in apis:
            if api["path"] == path and api["method"].upper() == method.upper():
                target_api = api
                break
        
        if not target_api:
            return Fail(code=404, msg="API不存在")
        
        # 尝试获取更详细的信息
        try:
            from api_versions.v2.routers import router as v2_router
            for route in v2_router.routes:
                if hasattr(route, 'path') and route.path == path:
                    if hasattr(route, 'endpoint'):
                        endpoint = route.endpoint
                        target_api["function_name"] = endpoint.__name__
                        target_api["module"] = endpoint.__module__
                        
                        # 获取函数签名
                        sig = inspect.signature(endpoint)
                        target_api["parameters"] = []
                        for param_name, param in sig.parameters.items():
                            param_info = {
                                "name": param_name,
                                "type": str(param.annotation) if param.annotation != inspect.Parameter.empty else "Any",
                                "default": str(param.default) if param.default != inspect.Parameter.empty else None
                            }
                            target_api["parameters"].append(param_info)
                    break
        except Exception as detail_error:
            logger.warning(f"获取API详细信息失败: {detail_error}")
        
        return Success(data=target_api)
    except Exception as e:
        logger.error(f"获取V2 API详情失败: {e}")
        return Fail(code=500, msg="获取API详情失败")
