"""
管理系统控制器
"""
from typing import List, Optional
from datetime import datetime
from fastapi import HTTPException
from tortoise.expressions import Q

from core.admin_crud import CRUDBase
from core.admin_models import User, Role, Menu, Api, Dept, AuditLog
from core.admin_schemas import (
    UserCreate, UserUpdate, RoleCreate, RoleUpdate,
    MenuCreate, MenuUpdate, ApiCreate, ApiUpdate,
    DeptCreate, DeptUpdate, CredentialsSchema
)
from core.admin_auth import verify_password, get_password_hash


class UserController(CRUDBase[User, UserCreate, UserUpdate]):
    def __init__(self):
        super().__init__(model=User)

    async def get_by_email(self, email: str) -> Optional[User]:
        """根据邮箱获取用户"""
        return await self.model.filter(email=email).first()

    async def get_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return await self.model.filter(username=username).first()

    async def create_user(self, obj_in: UserCreate) -> User:
        """创建用户"""
        # 检查用户名和邮箱是否已存在
        if await self.get_by_username(obj_in.username):
            raise HTTPException(status_code=400, detail="Username already exists")
        if await self.get_by_email(obj_in.email):
            raise HTTPException(status_code=400, detail="Email already exists")
        
        # 加密密码
        obj_in.password = get_password_hash(obj_in.password)
        
        # 创建用户（排除role_ids）
        user_data = obj_in.model_dump(exclude={'role_ids'})
        user = await self.model.create(**user_data)
        
        # 分配角色
        if obj_in.role_ids:
            await self.update_roles(user, obj_in.role_ids)
        
        return user

    async def update_user(self, user_id: int, obj_in: UserUpdate) -> User:
        """更新用户"""
        user = await self.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        # 检查用户名和邮箱唯一性（排除当前用户）
        if obj_in.username != user.username:
            if await self.model.filter(username=obj_in.username).exclude(id=user_id).exists():
                raise HTTPException(status_code=400, detail="Username already exists")
        
        if obj_in.email != user.email:
            if await self.model.filter(email=obj_in.email).exclude(id=user_id).exists():
                raise HTTPException(status_code=400, detail="Email already exists")
        
        # 更新用户信息（排除role_ids）
        user_data = obj_in.model_dump(exclude={'role_ids', 'id'})
        for field, value in user_data.items():
            setattr(user, field, value)
        
        await user.save()
        
        # 更新角色
        await self.update_roles(user, obj_in.role_ids)
        
        return user

    async def update_last_login(self, user_id: int) -> None:
        """更新最后登录时间"""
        user = await self.get(user_id)
        if user:
            user.last_login = datetime.now()
            await user.save()

    async def authenticate(self, credentials: CredentialsSchema) -> User:
        """用户认证"""
        user = await self.get_by_username(credentials.username)
        if not user:
            raise HTTPException(status_code=400, detail="Invalid username")
        
        if not verify_password(credentials.password, user.password):
            raise HTTPException(status_code=400, detail="Invalid password")
        
        if not user.is_active:
            raise HTTPException(status_code=400, detail="User is inactive")
        
        return user

    async def update_roles(self, user: User, role_ids: List[int]) -> None:
        """更新用户角色"""
        await user.roles.clear()
        for role_id in role_ids:
            role = await Role.get_or_none(id=role_id)
            if role:
                await user.roles.add(role)

    async def reset_password(self, user_id: int, new_password: str = "123456") -> None:
        """重置密码"""
        user = await self.get(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="User not found")
        
        user.password = get_password_hash(new_password)
        await user.save()


class RoleController(CRUDBase[Role, RoleCreate, RoleUpdate]):
    def __init__(self):
        super().__init__(model=Role)

    async def is_exist(self, name: str) -> bool:
        """检查角色名是否存在"""
        return await self.model.filter(name=name).exists()

    async def create_role(self, obj_in: RoleCreate) -> Role:
        """创建角色"""
        if await self.is_exist(obj_in.name):
            raise HTTPException(status_code=400, detail="Role name already exists")
        
        # 创建角色（排除关联字段）
        role_data = obj_in.model_dump(exclude={'menu_ids', 'api_infos'})
        role = await self.model.create(**role_data)
        
        # 分配权限
        await self.update_permissions(role, obj_in.menu_ids, obj_in.api_infos)
        
        return role

    async def update_role(self, role_id: int, obj_in: RoleUpdate) -> Role:
        """更新角色"""
        role = await self.get(role_id)
        if not role:
            raise HTTPException(status_code=404, detail="Role not found")
        
        # 检查角色名唯一性（排除当前角色）
        if obj_in.name != role.name:
            if await self.model.filter(name=obj_in.name).exclude(id=role_id).exists():
                raise HTTPException(status_code=400, detail="Role name already exists")
        
        # 更新角色信息
        role_data = obj_in.model_dump(exclude={'menu_ids', 'api_infos', 'id'})
        for field, value in role_data.items():
            setattr(role, field, value)
        
        await role.save()
        
        # 更新权限
        await self.update_permissions(role, obj_in.menu_ids, obj_in.api_infos)
        
        return role

    async def update_permissions(self, role: Role, menu_ids: List[int], api_infos: List[dict]) -> None:
        """更新角色权限"""
        # 清除现有权限
        await role.menus.clear()
        await role.apis.clear()
        
        # 分配菜单权限
        for menu_id in menu_ids:
            menu = await Menu.get_or_none(id=menu_id)
            if menu:
                await role.menus.add(menu)
        
        # 分配API权限
        for api_info in api_infos:
            api = await Api.filter(
                path=api_info.get("path"),
                method=api_info.get("method")
            ).first()
            if api:
                await role.apis.add(api)


class MenuController(CRUDBase[Menu, MenuCreate, MenuUpdate]):
    def __init__(self):
        super().__init__(model=Menu)

    async def get_by_path(self, path: str) -> Optional[Menu]:
        """根据路径获取菜单"""
        return await self.model.filter(path=path).first()

    async def get_menu_tree(self, name: Optional[str] = None) -> List[dict]:
        """获取菜单树"""
        query = Q()
        if name:
            query &= Q(name__contains=name)
        
        # 获取所有菜单
        menus = await self.model.filter(query).order_by("order")
        
        # 构建菜单树
        menu_dict = {}
        for menu in menus:
            menu_data = {
                "id": menu.id,
                "name": menu.name,
                "menu_type": menu.menu_type,
                "icon": menu.icon,
                "path": menu.path,
                "order": menu.order,
                "parent_id": menu.parent_id,
                "is_hidden": menu.is_hidden,
                "component": menu.component,
                "keepalive": menu.keepalive,
                "redirect": menu.redirect,
                "children": []
            }
            menu_dict[menu.id] = menu_data
        
        # 构建树形结构
        tree = []
        for menu_data in menu_dict.values():
            if menu_data["parent_id"] == 0:
                tree.append(menu_data)
            else:
                parent = menu_dict.get(menu_data["parent_id"])
                if parent:
                    parent["children"].append(menu_data)
        
        return tree


class ApiController(CRUDBase[Api, ApiCreate, ApiUpdate]):
    def __init__(self):
        super().__init__(model=Api)

    async def refresh_apis(self, app) -> None:
        """刷新API列表"""
        from fastapi.routing import APIRoute
        
        for route in app.routes:
            if isinstance(route, APIRoute) and len(route.dependencies) > 0:
                method = list(route.methods)[0]
                path = route.path_format
                summary = route.summary
                tags = list(route.tags)[0] if route.tags else None
                
                # 检查API是否已存在
                api = await self.model.filter(method=method, path=path).first()
                if api:
                    # 更新现有API
                    api.summary = summary
                    api.tags = tags
                    await api.save()
                else:
                    # 创建新API
                    await self.model.create(
                        method=method,
                        path=path,
                        summary=summary,
                        tags=tags
                    )


class DeptController(CRUDBase[Dept, DeptCreate, DeptUpdate]):
    def __init__(self):
        super().__init__(model=Dept)

    async def get_dept_tree(self, name: Optional[str] = None) -> List[dict]:
        """获取部门树"""
        query = Q()
        if name:
            query &= Q(name__contains=name)
        
        # 获取所有部门
        depts = await self.model.filter(query).order_by("order")
        
        # 构建部门树
        dept_dict = {}
        for dept in depts:
            dept_data = {
                "id": dept.id,
                "name": dept.name,
                "description": dept.description,
                "parent_id": dept.parent_id,
                "order": dept.order,
                "is_active": dept.is_active,
                "children": []
            }
            dept_dict[dept.id] = dept_data
        
        # 构建树形结构
        tree = []
        for dept_data in dept_dict.values():
            if dept_data["parent_id"] == 0:
                tree.append(dept_data)
            else:
                parent = dept_dict.get(dept_data["parent_id"])
                if parent:
                    parent["children"].append(dept_data)
        
        return tree


# 控制器实例
user_controller = UserController()
role_controller = RoleController()
menu_controller = MenuController()
api_controller = ApiController()
dept_controller = DeptController()
