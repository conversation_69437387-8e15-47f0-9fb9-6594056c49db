const Layout = () => import('@/layout/index.vue')

export default {
  name: 'DigitalHuman',
  path: '/digital-human',
  component: Layout,
  redirect: '/digital-human/v2-apis',
  meta: {
    title: '数字人管理',
    icon: 'mdi:robot-outline',
    order: 2,
  },
  children: [
    {
      name: 'V2APIs',
      path: 'v2-apis',
      component: () => import('./v2-apis/index.vue'),
      meta: {
        title: 'V2 API管理',
        icon: 'mdi:api',
      },
    },
    {
      name: 'Environment',
      path: 'environment',
      component: () => import('./environment/index.vue'),
      meta: {
        title: '环境配置',
        icon: 'mdi:cog-outline',
      },
    },
    {
      name: 'Devices',
      path: 'devices',
      component: () => import('./devices/index.vue'),
      meta: {
        title: '设备管理',
        icon: 'mdi:devices',
      },
    },
  ],
}
