import asyncio
from fastapi import Fast<PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from .lifespan import lifespan
from core.logger import logger
from tortoise.contrib.fastapi import register_tortoise
from settings.tortoise_config import TORTOISE_ORM
from api_versions.v1.routers import router as v1_router
from api_versions.v2.routers import router as v2_router
from api_versions.utils.routers import router as utils_router
try:
    from api_versions.admin.v2_management import router as admin_router
except ImportError:
    # 如果admin模块不存在，创建一个空的路由
    from fastapi import APIRouter
    admin_router = APIRouter()



def create_app() -> FastAPI:
    app:FastAPI = FastAPI(
        title="数字人项目后端接口",
        description="功能不断增加中...",
        docs_url="/",
        version="0.0.1",
        lifespan=lifespan,
        swagger_ui_parameters={"defaultModelsExpandDepth": -1}
    )

    @app.get("/health", status_code=200, include_in_schema=False)
    def health():
        return {"status": "200", "message": "health"}

    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        max_age=3600
    )

    register_tortoise(
        app,
        config=TORTOISE_ORM,
        generate_schemas=False,
        add_exception_handlers=True

    )

    @app.middleware("http")
    async def suppress_disconnect_errors(request: Request, call_next):
        try:
            return await call_next(request)
        except ConnectionResetError as e:
            logger.debug(f"客户端提前断开连接: {request.client} - {e}")

    @app.middleware("http")
    async def user_context_middleware(request:Request, call_next):

        # logger.info(f"请求头: {request.headers}")

        user_id = request.headers.get("user_id", "anonymous")
        reference_id = request.headers.get("reference_id")
        api_key = request.headers.get("dify_api_key", "")
        mode = request.headers.get("mode", '')
        request.state.user_id = user_id # 注入到请求状态
        request.state.reference_id = reference_id
        request.state.api_key = api_key
        request.state.mode = mode

        request.state.streaming_lock = asyncio.Lock()

        response = await call_next(request)
        return response

    app.mount("/static", StaticFiles(directory="./static"), name="audio_files")
    app.include_router(router=v1_router, prefix="/v1", tags=['V1版本（已弃用）'])
    app.include_router(router=v2_router, prefix="/v2", tags=['V2版本'])
    app.include_router(router=utils_router, prefix="/utils", tags=['工具接口'])
    app.include_router(router=admin_router, prefix="/admin", tags=['管理接口'])

    return app