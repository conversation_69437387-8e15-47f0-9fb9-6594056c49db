from contextlib import asynccontextmanager
from fastapi import FastAPI
from app.lifespan_tools import init_start_lifespan
from tortoise import Tortoise, run_async
from settings.tortoise_config import TORTOISE_ORM
from settings.config import settings
try:
    from core.admin_init import init_admin_data
except ImportError:
    async def init_admin_data():
        pass


async def start_app():
    print("✅ fastapi已启动")
    await init_start_lifespan()

    # 初始化管理系统数据
    try:
        await init_admin_data()
        print("✅ 管理系统初始化完成")
    except Exception as e:
        print(f"⚠️ 管理系统初始化失败: {e}")
    
async def shutdown():
    print("❌ fastapi已关闭")
    if settings.enable_database:
        await Tortoise.close_connections()
        print("❌ 数据库连接已关闭")

@asynccontextmanager
async def lifespan(app: FastAPI):
    await start_app()
    yield
    await shutdown()


