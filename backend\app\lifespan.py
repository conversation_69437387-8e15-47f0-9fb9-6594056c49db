from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from app.lifespan_tools import init_start_lifespan
from tortoise import Tortoise, run_async
from settings.tortoise_config import TORTOISE_ORM
from settings.config import settings


async def start_app():
    print("✅ fastapi已启动")
    await init_start_lifespan()
    
async def shutdown():
    print("❌ fastapi已关闭")
    if settings.enable_database:
        await Tortoise.close_connections()
        print("❌ 数据库连接已关闭")

@asynccontextmanager
async def lifespan(app: FastAPI):
    await start_app()
    yield
    await shutdown()


