<template>
  <div class="v2-apis-container">
    <n-card title="V2 API管理" :bordered="false" size="small" class="h-full shadow-sm rounded-16px">
      <!-- 统计卡片 -->
      <div class="mb-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
        <n-card size="small" class="shadow-sm">
          <n-statistic label="总API数" :value="stats.total" />
        </n-card>
        <n-card size="small" class="shadow-sm">
          <n-statistic label="已启用" :value="stats.enabled" />
        </n-card>
        <n-card size="small" class="shadow-sm">
          <n-statistic label="已禁用" :value="stats.disabled" />
        </n-card>
      </div>

      <!-- 查询表单 -->
      <n-form ref="queryFormRef" inline :label-width="80" :model="queryForm" class="mb-4">
        <n-form-item label="API路径" path="path">
          <n-input v-model:value="queryForm.path" placeholder="请输入API路径" clearable />
        </n-form-item>
        <n-form-item label="请求方法" path="method">
          <n-select
            v-model:value="queryForm.method"
            placeholder="请选择请求方法"
            :options="methodOptions"
            clearable
          />
        </n-form-item>
        <n-form-item label="标签" path="tags">
          <n-select
            v-model:value="queryForm.tags"
            placeholder="请选择标签"
            :options="tagOptions"
            clearable
          />
        </n-form-item>
        <n-form-item>
          <n-button type="primary" @click="handleQuery">
            <template #icon>
              <i class="i-material-symbols:search" />
            </template>
            查询
          </n-button>
          <n-button class="ml-2" @click="handleReset">
            <template #icon>
              <i class="i-material-symbols:refresh" />
            </template>
            重置
          </n-button>
          <n-button class="ml-2" @click="handleRefresh">
            <template #icon>
              <i class="i-material-symbols:sync" />
            </template>
            刷新API
          </n-button>
        </n-form-item>
      </n-form>

      <!-- 数据表格 -->
      <n-data-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        :pagination="pagination"
        :row-key="(row) => `${row.method}-${row.path}`"
        flex-height
        class="h-480px"
      />
    </n-card>

    <!-- API详情抽屉 -->
    <n-drawer v-model:show="showDetail" :width="600" placement="right">
      <n-drawer-content title="API详情">
        <div v-if="currentApi">
          <n-descriptions :column="1" bordered>
            <n-descriptions-item label="路径">{{ currentApi.path }}</n-descriptions-item>
            <n-descriptions-item label="方法">
              <n-tag :type="getMethodType(currentApi.method)">{{ currentApi.method }}</n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="摘要">{{ currentApi.summary || '无' }}</n-descriptions-item>
            <n-descriptions-item label="描述">{{ currentApi.description || '无' }}</n-descriptions-item>
            <n-descriptions-item label="标签">
              <n-space>
                <n-tag v-for="tag in currentApi.tags" :key="tag" size="small">{{ tag }}</n-tag>
              </n-space>
            </n-descriptions-item>
            <n-descriptions-item label="状态">
              <n-tag :type="currentApi.enabled ? 'success' : 'error'">
                {{ currentApi.enabled ? '已启用' : '已禁用' }}
              </n-tag>
            </n-descriptions-item>
          </n-descriptions>

          <div v-if="currentApi.parameters" class="mt-4">
            <h4 class="mb-2">参数信息</h4>
            <n-table size="small">
              <thead>
                <tr>
                  <th>参数名</th>
                  <th>类型</th>
                  <th>默认值</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="param in currentApi.parameters" :key="param.name">
                  <td>{{ param.name }}</td>
                  <td>{{ param.type }}</td>
                  <td>{{ param.default || '无' }}</td>
                </tr>
              </tbody>
            </n-table>
          </div>
        </div>
      </n-drawer-content>
    </n-drawer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, h } from 'vue'
import { NButton, NTag, NSwitch, useMessage } from 'naive-ui'
import api from '@/api'

const message = useMessage()

// 响应式数据
const loading = ref(false)
const tableData = ref([])
const stats = ref({ total: 0, enabled: 0, disabled: 0 })
const showDetail = ref(false)
const currentApi = ref(null)

// 查询表单
const queryForm = reactive({
  path: '',
  method: '',
  tags: ''
})

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 20, 50],
  onChange: (page) => {
    pagination.page = page
    getTableData()
  },
  onUpdatePageSize: (pageSize) => {
    pagination.pageSize = pageSize
    pagination.page = 1
    getTableData()
  }
})

// 选项数据
const methodOptions = ref([])
const tagOptions = ref([])

// 表格列定义
const columns = [
  { title: '路径', key: 'path', width: 200, ellipsis: { tooltip: true } },
  {
    title: '方法',
    key: 'method',
    width: 80,
    render: (row) => h(NTag, { type: getMethodType(row.method) }, { default: () => row.method })
  },
  { title: '摘要', key: 'summary', ellipsis: { tooltip: true } },
  {
    title: '标签',
    key: 'tags',
    width: 150,
    render: (row) => {
      if (!row.tags || row.tags.length === 0) return '无'
      return h('div', row.tags.map(tag => 
        h(NTag, { size: 'small', class: 'mr-1' }, { default: () => tag })
      ))
    }
  },
  {
    title: '状态',
    key: 'enabled',
    width: 100,
    render: (row) => h(
      NSwitch,
      {
        value: row.enabled,
        onUpdateValue: (value) => handleToggleStatus(row, value)
      }
    )
  },
  {
    title: '操作',
    key: 'actions',
    width: 120,
    render: (row) => h(
      NButton,
      {
        size: 'small',
        type: 'primary',
        onClick: () => handleViewDetail(row)
      },
      { default: () => '查看详情' }
    )
  }
]

// 方法类型映射
const getMethodType = (method) => {
  const typeMap = {
    GET: 'info',
    POST: 'success',
    PUT: 'warning',
    DELETE: 'error',
    PATCH: 'warning'
  }
  return typeMap[method] || 'default'
}

// 获取表格数据
const getTableData = async () => {
  try {
    loading.value = true
    const params = {
      page: pagination.page,
      page_size: pagination.pageSize,
      ...queryForm
    }
    const res = await api.getV2Apis(params)
    tableData.value = res.data
    pagination.itemCount = res.total
  } catch (error) {
    message.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 获取统计数据
const getStats = async () => {
  try {
    const res = await api.getV2ApiStats()
    stats.value = res.data
  } catch (error) {
    message.error('获取统计数据失败')
  }
}

// 获取类型数据
const getTypes = async () => {
  try {
    const res = await api.getV2ApiTypes()
    methodOptions.value = res.data.methods.map(method => ({ label: method, value: method }))
    tagOptions.value = res.data.tags.map(tag => ({ label: tag, value: tag }))
  } catch (error) {
    message.error('获取类型数据失败')
  }
}

// 查询
const handleQuery = () => {
  pagination.page = 1
  getTableData()
}

// 重置
const handleReset = () => {
  Object.assign(queryForm, { path: '', method: '', tags: '' })
  pagination.page = 1
  getTableData()
}

// 刷新API
const handleRefresh = async () => {
  try {
    loading.value = true
    await api.refreshV2Api()
    message.success('刷新成功')
    await Promise.all([getTableData(), getStats(), getTypes()])
  } catch (error) {
    message.error('刷新失败')
  } finally {
    loading.value = false
  }
}

// 切换状态
const handleToggleStatus = async (row, enabled) => {
  try {
    await api.toggleV2Api({ path: row.path, method: row.method, enabled })
    row.enabled = enabled
    message.success(`API已${enabled ? '启用' : '禁用'}`)
    getStats()
  } catch (error) {
    message.error('操作失败')
  }
}

// 查看详情
const handleViewDetail = async (row) => {
  try {
    const res = await api.getV2ApiDetail({ path: row.path, method: row.method })
    currentApi.value = res.data
    showDetail.value = true
  } catch (error) {
    message.error('获取详情失败')
  }
}

// 初始化
onMounted(async () => {
  await Promise.all([getTableData(), getStats(), getTypes()])
})
</script>

<style scoped>
.v2-apis-container {
  padding: 16px;
}
</style>
