#!/usr/bin/env python3
"""
测试管理系统功能
"""
import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_v2_api_discovery():
    """测试V2 API发现功能"""
    try:
        from api_versions.admin.v2_management import get_v2_api_info
        
        print("🔍 测试V2 API发现功能...")
        apis = get_v2_api_info()
        
        print(f"✅ 发现 {len(apis)} 个V2 API:")
        for api in apis[:5]:  # 只显示前5个
            print(f"  - {api['method']} {api['path']} - {api['summary']}")
        
        if len(apis) > 5:
            print(f"  ... 还有 {len(apis) - 5} 个API")
            
        return True
    except Exception as e:
        print(f"❌ V2 API发现测试失败: {e}")
        return False


async def test_admin_models():
    """测试管理系统模型"""
    try:
        from core.admin_models import User, Role, Menu
        
        print("🔍 测试管理系统模型...")
        
        # 测试模型定义
        print("✅ User模型定义正常")
        print("✅ Role模型定义正常") 
        print("✅ Menu模型定义正常")
        
        return True
    except Exception as e:
        print(f"❌ 管理系统模型测试失败: {e}")
        return False


async def test_admin_schemas():
    """测试管理系统数据模式"""
    try:
        from core.admin_schemas import Success, UserCreate, V2ApiInfo
        
        print("🔍 测试管理系统数据模式...")
        
        # 测试响应模式
        success_response = Success(data={"test": "data"})
        print(f"✅ Success响应: {success_response.model_dump()}")
        
        # 测试V2 API信息模式
        api_info = V2ApiInfo(
            path="/test",
            method="GET",
            summary="测试API"
        )
        print(f"✅ V2ApiInfo: {api_info.model_dump()}")
        
        return True
    except Exception as e:
        print(f"❌ 管理系统数据模式测试失败: {e}")
        return False


async def test_admin_auth():
    """测试管理系统认证"""
    try:
        from core.admin_auth import get_password_hash, verify_password, create_access_token
        
        print("🔍 测试管理系统认证...")
        
        # 测试密码加密
        password = "test123"
        hashed = get_password_hash(password)
        print(f"✅ 密码加密: {password} -> {hashed[:20]}...")
        
        # 测试密码验证
        is_valid = verify_password(password, hashed)
        print(f"✅ 密码验证: {is_valid}")
        
        # 测试JWT令牌创建
        token = create_access_token({"user_id": 1, "username": "test"})
        print(f"✅ JWT令牌: {token[:20]}...")
        
        return True
    except Exception as e:
        print(f"❌ 管理系统认证测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始测试管理系统集成...")
    print("=" * 50)
    
    tests = [
        ("V2 API发现", test_v2_api_discovery),
        ("管理系统模型", test_admin_models),
        ("数据模式", test_admin_schemas),
        ("认证系统", test_admin_auth),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总计: {passed}/{len(results)} 个测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！管理系统集成成功！")
        print("\n📝 下一步:")
        print("1. 启动后端服务: python start_admin.py")
        print("2. 启动前端服务: cd vue-fastapi-admin/web && pnpm run dev")
        print("3. 访问管理界面: http://localhost:3100")
        print("4. 默认账号: admin / 123456")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
