hoistPattern:
  - '*'
hoistedDependencies:
  '@ampproject/remapping@2.3.0':
    '@ampproject/remapping': private
  '@antfu/install-pkg@0.1.1':
    '@antfu/install-pkg': private
  '@antfu/utils@0.7.10':
    '@antfu/utils': private
  '@babel/helper-string-parser@7.24.8':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.24.7':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.25.0':
    '@babel/parser': private
  '@babel/runtime@7.25.0':
    '@babel/runtime': private
  '@babel/types@7.25.2':
    '@babel/types': private
  '@css-render/plugin-bem@0.15.14(css-render@0.15.14)':
    '@css-render/plugin-bem': private
  '@css-render/vue3-ssr@0.15.14(vue@3.4.34(typescript@5.5.4))':
    '@css-render/vue3-ssr': private
  '@emotion/hash@0.8.0':
    '@emotion/hash': private
  '@esbuild/android-arm64@0.18.20':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.18.20':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.18.20':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.18.20':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.18.20':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.18.20':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.18.20':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.18.20':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.18.20':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.18.20':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.18.20':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.18.20':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.18.20':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.18.20':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.18.20':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.18.20':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.18.20':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.18.20':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.18.20':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.18.20':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.18.20':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.18.20':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.4.0(eslint@8.57.0)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.11.0':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.0':
    '@eslint/js': private
  '@humanwhocodes/config-array@0.11.14':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@iconify/types@2.0.0':
    '@iconify/types': private
  '@iconify/utils@2.1.29':
    '@iconify/utils': private
  '@intlify/core-base@9.13.1':
    '@intlify/core-base': private
  '@intlify/message-compiler@9.13.1':
    '@intlify/message-compiler': private
  '@intlify/shared@9.13.1':
    '@intlify/shared': private
  '@jridgewell/gen-mapping@0.3.5':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@juggle/resize-observer@3.4.0':
    '@juggle/resize-observer': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.1.1':
    '@pkgr/core': private
  '@polka/url@1.0.0-next.25':
    '@polka/url': private
  '@rollup/pluginutils@5.1.0(rollup@3.29.4)':
    '@rollup/pluginutils': private
  '@trysound/sax@0.2.0':
    '@trysound/sax': private
  '@types/estree@1.0.5':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/katex@0.16.7':
    '@types/katex': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.7':
    '@types/lodash': private
  '@types/node@22.0.0':
    '@types/node': private
  '@types/semver@7.5.8':
    '@types/semver': private
  '@types/svgo@2.6.4':
    '@types/svgo': private
  '@types/web-bluetooth@0.0.20':
    '@types/web-bluetooth': private
  '@typescript-eslint/scope-manager@6.21.0':
    '@typescript-eslint/scope-manager': private
  '@typescript-eslint/types@6.21.0':
    '@typescript-eslint/types': private
  '@typescript-eslint/typescript-estree@6.21.0(typescript@5.5.4)':
    '@typescript-eslint/typescript-estree': private
  '@typescript-eslint/utils@6.21.0(eslint@8.57.0)(typescript@5.5.4)':
    '@typescript-eslint/utils': private
  '@typescript-eslint/visitor-keys@6.21.0':
    '@typescript-eslint/visitor-keys': private
  '@ungap/structured-clone@1.2.0':
    '@ungap/structured-clone': private
  '@unocss/astro@0.55.7(rollup@3.29.4)(vite@4.5.3(@types/node@22.0.0)(sass@1.77.8)(terser@5.31.3))':
    '@unocss/astro': private
  '@unocss/cli@0.55.7(rollup@3.29.4)':
    '@unocss/cli': private
  '@unocss/config@0.55.7':
    '@unocss/config': private
  '@unocss/core@0.55.7':
    '@unocss/core': private
  '@unocss/eslint-plugin@0.55.7(eslint@8.57.0)(typescript@5.5.4)':
    '@unocss/eslint-plugin': private
  '@unocss/extractor-arbitrary-variants@0.55.7':
    '@unocss/extractor-arbitrary-variants': private
  '@unocss/inspector@0.55.7':
    '@unocss/inspector': private
  '@unocss/postcss@0.55.7(postcss@5.2.18)':
    '@unocss/postcss': private
  '@unocss/preset-attributify@0.55.7':
    '@unocss/preset-attributify': private
  '@unocss/preset-icons@0.55.7':
    '@unocss/preset-icons': private
  '@unocss/preset-mini@0.55.7':
    '@unocss/preset-mini': private
  '@unocss/preset-tagify@0.55.7':
    '@unocss/preset-tagify': private
  '@unocss/preset-typography@0.55.7':
    '@unocss/preset-typography': private
  '@unocss/preset-uno@0.55.7':
    '@unocss/preset-uno': private
  '@unocss/preset-web-fonts@0.55.7':
    '@unocss/preset-web-fonts': private
  '@unocss/preset-wind@0.55.7':
    '@unocss/preset-wind': private
  '@unocss/reset@0.55.7':
    '@unocss/reset': private
  '@unocss/scope@0.55.7':
    '@unocss/scope': private
  '@unocss/transformer-attributify-jsx-babel@0.55.7':
    '@unocss/transformer-attributify-jsx-babel': private
  '@unocss/transformer-attributify-jsx@0.55.7':
    '@unocss/transformer-attributify-jsx': private
  '@unocss/transformer-compile-class@0.55.7':
    '@unocss/transformer-compile-class': private
  '@unocss/transformer-directives@0.55.7':
    '@unocss/transformer-directives': private
  '@unocss/transformer-variant-group@0.55.7':
    '@unocss/transformer-variant-group': private
  '@unocss/vite@0.55.7(rollup@3.29.4)(vite@4.5.3(@types/node@22.0.0)(sass@1.77.8)(terser@5.31.3))':
    '@unocss/vite': private
  '@vue/compiler-core@3.4.34':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.4.34':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.4.34':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.4.34':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.3':
    '@vue/devtools-api': private
  '@vue/reactivity@3.4.34':
    '@vue/reactivity': private
  '@vue/runtime-core@3.4.34':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.4.34':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.4.34(vue@3.4.34(typescript@5.5.4))':
    '@vue/server-renderer': private
  '@vue/shared@3.4.34':
    '@vue/shared': private
  '@vueuse/metadata@10.11.0':
    '@vueuse/metadata': private
  '@vueuse/shared@10.11.0(vue@3.4.34(typescript@5.5.4))':
    '@vueuse/shared': private
  acorn-jsx@5.3.2(acorn@8.12.1):
    acorn-jsx: private
  acorn@8.12.1:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  anymatch@3.1.3:
    anymatch: private
  argparse@2.0.1:
    argparse: private
  arr-diff@4.0.0:
    arr-diff: private
  arr-flatten@1.1.0:
    arr-flatten: private
  arr-union@3.1.0:
    arr-union: private
  array-buffer-byte-length@1.0.1:
    array-buffer-byte-length: private
  array-union@2.1.0:
    array-union: private
  array-unique@0.3.2:
    array-unique: private
  arraybuffer.prototype.slice@1.0.3:
    arraybuffer.prototype.slice: private
  assign-symbols@1.0.0:
    assign-symbols: private
  async-validator@4.2.5:
    async-validator: private
  async@3.2.5:
    async: private
  asynckit@0.4.0:
    asynckit: private
  atob@2.1.2:
    atob: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  balanced-match@1.0.2:
    balanced-match: private
  base@0.11.2:
    base: private
  big.js@5.2.2:
    big.js: private
  binary-extensions@2.3.0:
    binary-extensions: private
  bluebird@3.7.2:
    bluebird: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.11:
    brace-expansion: private
  braces@3.0.3:
    braces: private
  buffer-from@1.1.2:
    buffer-from: private
  cac@6.7.14:
    cac: private
  cache-base@1.0.1:
    cache-base: private
  call-bind@1.0.7:
    call-bind: private
  callsites@3.1.0:
    callsites: private
  camel-case@4.1.2:
    camel-case: private
  chalk@4.1.2:
    chalk: private
  chokidar@3.6.0:
    chokidar: private
  class-utils@0.3.6:
    class-utils: private
  clean-css@5.3.3:
    clean-css: private
  cliui@8.0.1:
    cliui: private
  clone@2.1.2:
    clone: private
  collection-visit@1.0.0:
    collection-visit: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  colorette@2.0.20:
    colorette: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@8.3.0:
    commander: private
  component-emitter@1.3.1:
    component-emitter: private
  concat-map@0.0.1:
    concat-map: private
  confbox@0.1.7:
    confbox: private
  connect-history-api-fallback@1.6.0:
    connect-history-api-fallback: private
  consola@2.15.3:
    consola: private
  copy-descriptor@0.1.1:
    copy-descriptor: private
  cors@2.8.5:
    cors: private
  cross-spawn@7.0.3:
    cross-spawn: private
  css-render@0.15.14:
    css-render: private
  css-select@4.3.0:
    css-select: private
  css-tree@2.3.1:
    css-tree: private
  css-what@6.1.0:
    css-what: private
  cssesc@3.0.0:
    cssesc: private
  csso@4.2.0:
    csso: private
  csstype@3.1.3:
    csstype: private
  data-view-buffer@1.0.1:
    data-view-buffer: private
  data-view-byte-length@1.0.1:
    data-view-byte-length: private
  data-view-byte-offset@1.0.0:
    data-view-byte-offset: private
  date-fns-tz@2.0.1(date-fns@2.30.0):
    date-fns-tz: private
  date-fns@2.30.0:
    date-fns: private
  debug@4.3.6:
    debug: private
  decode-uri-component@0.2.2:
    decode-uri-component: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-lazy-prop@2.0.0:
    define-lazy-prop: private
  define-properties@1.2.1:
    define-properties: private
  define-property@1.0.0:
    define-property: private
  defu@6.1.4:
    defu: private
  delayed-stream@1.0.0:
    delayed-stream: private
  destr@2.0.3:
    destr: private
  dir-glob@3.0.1:
    dir-glob: private
  doctrine@3.0.0:
    doctrine: private
  dom-serializer@1.4.1:
    dom-serializer: private
  domelementtype@2.3.0:
    domelementtype: private
  domhandler@4.3.1:
    domhandler: private
  domutils@2.8.0:
    domutils: private
  dot-case@3.0.4:
    dot-case: private
  dotenv-expand@8.0.3:
    dotenv-expand: private
  duplexer@0.1.2:
    duplexer: private
  ejs@3.1.10:
    ejs: private
  emoji-regex@8.0.0:
    emoji-regex: private
  emojis-list@3.0.0:
    emojis-list: private
  entities@4.5.0:
    entities: private
  es-abstract@1.23.3:
    es-abstract: private
  es-define-property@1.0.0:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.0.0:
    es-object-atoms: private
  es-set-tostringtag@2.0.3:
    es-set-tostringtag: private
  es-to-primitive@1.2.1:
    es-to-primitive: private
  esbuild@0.18.20:
    esbuild: private
  escalade@3.1.2:
    escalade: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-config-prettier@8.10.0(eslint@8.57.0):
    eslint-config-prettier: private
  eslint-plugin-prettier@4.2.1(eslint-config-prettier@8.10.0(eslint@8.57.0))(eslint@8.57.0)(prettier@2.8.8):
    eslint-plugin-prettier: private
  eslint-plugin-vue@9.27.0(eslint@8.57.0):
    eslint-plugin-vue: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  etag@1.8.1:
    etag: private
  evtd@0.2.4:
    evtd: private
  execa@5.1.1:
    execa: private
  expand-brackets@2.1.4:
    expand-brackets: private
  extend-shallow@2.0.1:
    extend-shallow: private
  extglob@2.0.4:
    extglob: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-glob@3.3.2:
    fast-glob: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.17.1:
    fastq: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  filelist@1.0.4:
    filelist: private
  fill-range@7.1.1:
    fill-range: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.1:
    flatted: private
  follow-redirects@1.15.6:
    follow-redirects: private
  for-each@0.3.3:
    for-each: private
  for-in@1.0.2:
    for-in: private
  form-data@4.0.0:
    form-data: private
  fragment-cache@0.2.1:
    fragment-cache: private
  fs-extra@10.1.0:
    fs-extra: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.6:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-caller-file@2.0.5:
    get-caller-file: private
  get-intrinsic@1.2.4:
    get-intrinsic: private
  get-stream@6.0.1:
    get-stream: private
  get-symbol-description@1.0.2:
    get-symbol-description: private
  get-value@2.0.6:
    get-value: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  globalthis@1.0.4:
    globalthis: private
  globby@11.1.0:
    globby: private
  gopd@1.0.1:
    gopd: private
  graceful-fs@4.2.11:
    graceful-fs: private
  graphemer@1.4.0:
    graphemer: private
  gzip-size@6.0.0:
    gzip-size: private
  has-ansi@2.0.0:
    has-ansi: private
  has-bigints@1.0.2:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.0.3:
    has-proto: private
  has-symbols@1.0.3:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  has-value@1.0.0:
    has-value: private
  has-values@1.0.0:
    has-values: private
  hasown@2.0.2:
    hasown: private
  he@1.2.0:
    he: private
  highlight.js@11.10.0:
    highlight.js: private
  html-minifier-terser@6.1.0:
    html-minifier-terser: private
  htmlparser2@3.10.1:
    htmlparser2: private
  human-signals@2.1.0:
    human-signals: private
  ignore@5.3.1:
    ignore: private
  image-size@0.5.5:
    image-size: private
  immutable@4.3.7:
    immutable: private
  import-fresh@3.3.0:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  internal-slot@1.0.7:
    internal-slot: private
  is-accessor-descriptor@1.0.1:
    is-accessor-descriptor: private
  is-array-buffer@3.0.4:
    is-array-buffer: private
  is-bigint@1.0.4:
    is-bigint: private
  is-binary-path@2.1.0:
    is-binary-path: private
  is-boolean-object@1.1.2:
    is-boolean-object: private
  is-buffer@1.1.6:
    is-buffer: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.15.0:
    is-core-module: private
  is-data-descriptor@1.0.1:
    is-data-descriptor: private
  is-data-view@1.0.1:
    is-data-view: private
  is-date-object@1.0.5:
    is-date-object: private
  is-descriptor@1.0.3:
    is-descriptor: private
  is-docker@2.2.1:
    is-docker: private
  is-extendable@0.1.1:
    is-extendable: private
  is-extglob@2.1.1:
    is-extglob: private
  is-fullwidth-code-point@3.0.0:
    is-fullwidth-code-point: private
  is-glob@4.0.3:
    is-glob: private
  is-negative-zero@2.0.3:
    is-negative-zero: private
  is-number-object@1.0.7:
    is-number-object: private
  is-number@3.0.0:
    is-number: private
  is-path-inside@3.0.3:
    is-path-inside: private
  is-plain-obj@1.1.0:
    is-plain-obj: private
  is-plain-object@2.0.4:
    is-plain-object: private
  is-regex@1.1.4:
    is-regex: private
  is-shared-array-buffer@1.0.3:
    is-shared-array-buffer: private
  is-stream@2.0.1:
    is-stream: private
  is-string@1.0.7:
    is-string: private
  is-symbol@1.0.4:
    is-symbol: private
  is-typed-array@1.1.13:
    is-typed-array: private
  is-weakref@1.0.2:
    is-weakref: private
  is-windows@1.0.2:
    is-windows: private
  is-wsl@2.2.0:
    is-wsl: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  isobject@3.0.1:
    isobject: private
  jake@10.9.2:
    jake: private
  jiti@1.21.6:
    jiti: private
  js-base64@2.6.4:
    js-base64: private
  js-tokens@9.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  json5@1.0.2:
    json5: private
  jsonfile@6.1.0:
    jsonfile: private
  keyv@4.5.4:
    keyv: private
  kind-of@5.1.0:
    kind-of: private
  kolorist@1.8.0:
    kolorist: private
  levn@0.4.1:
    levn: private
  loader-utils@1.4.2:
    loader-utils: private
  local-pkg@0.5.0:
    local-pkg: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  lower-case@2.0.2:
    lower-case: private
  magic-string@0.30.11:
    magic-string: private
  map-cache@0.2.2:
    map-cache: private
  map-visit@1.0.0:
    map-visit: private
  mdn-data@2.0.14:
    mdn-data: private
  merge-options@1.0.1:
    merge-options: private
  merge-stream@2.0.0:
    merge-stream: private
  merge2@1.4.1:
    merge2: private
  micromatch@4.0.7:
    micromatch: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  mimic-fn@2.1.0:
    mimic-fn: private
  minimatch@3.1.2:
    minimatch: private
  minimist@1.2.8:
    minimist: private
  mixin-deep@1.3.2:
    mixin-deep: private
  mlly@1.7.1:
    mlly: private
  mrmime@2.0.0:
    mrmime: private
  ms@2.1.2:
    ms: private
  nanoid@3.3.7:
    nanoid: private
  nanomatch@1.2.13:
    nanomatch: private
  natural-compare@1.4.0:
    natural-compare: private
  no-case@3.0.4:
    no-case: private
  node-fetch-native@1.6.4:
    node-fetch-native: private
  node-html-parser@5.4.2:
    node-html-parser: private
  normalize-path@3.0.0:
    normalize-path: private
  npm-run-path@4.0.1:
    npm-run-path: private
  nth-check@2.1.1:
    nth-check: private
  object-assign@4.1.1:
    object-assign: private
  object-copy@0.1.0:
    object-copy: private
  object-inspect@1.13.2:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object-visit@1.0.1:
    object-visit: private
  object.assign@4.1.5:
    object.assign: private
  object.pick@1.3.0:
    object.pick: private
  ofetch@1.3.4:
    ofetch: private
  once@1.4.0:
    once: private
  onetime@5.1.2:
    onetime: private
  open@8.4.2:
    open: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  param-case@3.0.4:
    param-case: private
  parent-module@1.0.1:
    parent-module: private
  pascal-case@3.1.2:
    pascal-case: private
  pascalcase@0.1.1:
    pascalcase: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  path-type@4.0.0:
    path-type: private
  pathe@1.1.2:
    pathe: private
  perfect-debounce@1.0.0:
    perfect-debounce: private
  picocolors@1.0.1:
    picocolors: private
  picomatch@2.3.1:
    picomatch: private
  pkg-types@1.1.3:
    pkg-types: private
  posix-character-classes@0.1.1:
    posix-character-classes: private
  possible-typed-array-names@1.0.0:
    possible-typed-array-names: private
  postcss-prefix-selector@1.16.1(postcss@5.2.18):
    postcss-prefix-selector: private
  postcss-selector-parser@6.1.1:
    postcss-selector-parser: private
  postcss@8.4.40:
    postcss: private
  posthtml-parser@0.2.1:
    posthtml-parser: private
  posthtml-rename-id@1.0.12:
    posthtml-rename-id: private
  posthtml-render@1.4.0:
    posthtml-render: private
  posthtml-svg-mode@1.0.3:
    posthtml-svg-mode: private
  posthtml@0.9.2:
    posthtml: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  prettier@2.8.8:
    prettier: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  query-string@4.3.4:
    query-string: private
  queue-microtask@1.2.3:
    queue-microtask: private
  readable-stream@3.6.2:
    readable-stream: private
  readdirp@3.6.0:
    readdirp: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regex-not@1.0.2:
    regex-not: private
  regexp.prototype.flags@1.5.2:
    regexp.prototype.flags: private
  relateurl@0.2.7:
    relateurl: private
  repeat-element@1.1.4:
    repeat-element: private
  repeat-string@1.6.1:
    repeat-string: private
  require-directory@2.1.1:
    require-directory: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve-url@0.2.1:
    resolve-url: private
  resolve@1.22.8:
    resolve: private
  ret@0.1.15:
    ret: private
  reusify@1.0.4:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@3.29.4:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  safe-array-concat@1.1.2:
    safe-array-concat: private
  safe-buffer@5.2.1:
    safe-buffer: private
  safe-regex-test@1.0.3:
    safe-regex-test: private
  safe-regex@1.1.0:
    safe-regex: private
  scule@1.3.0:
    scule: private
  seemly@0.3.8:
    seemly: private
  semver@7.6.3:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-value@2.0.1:
    set-value: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel@1.0.6:
    side-channel: private
  signal-exit@3.0.7:
    signal-exit: private
  sirv@2.0.4:
    sirv: private
  slash@3.0.0:
    slash: private
  snapdragon-node@2.1.1:
    snapdragon-node: private
  snapdragon-util@3.0.1:
    snapdragon-util: private
  snapdragon@0.8.2:
    snapdragon: private
  source-map-js@1.2.0:
    source-map-js: private
  source-map-resolve@0.5.3:
    source-map-resolve: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map-url@0.4.1:
    source-map-url: private
  source-map@0.7.4:
    source-map: private
  split-string@3.1.0:
    split-string: private
  stable@0.1.8:
    stable: private
  static-extend@0.1.2:
    static-extend: private
  strict-uri-encode@1.1.0:
    strict-uri-encode: private
  string-width@4.2.3:
    string-width: private
  string.prototype.trim@1.2.9:
    string.prototype.trim: private
  string.prototype.trimend@1.0.8:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  string_decoder@1.3.0:
    string_decoder: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-final-newline@2.0.0:
    strip-final-newline: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  strip-literal@2.1.0:
    strip-literal: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  svg-baker@1.7.0:
    svg-baker: private
  svgo@2.8.0:
    svgo: private
  synckit@0.8.8:
    synckit: private
  terser@5.31.3:
    terser: private
  text-table@0.2.0:
    text-table: private
  to-fast-properties@2.0.0:
    to-fast-properties: private
  to-object-path@0.3.0:
    to-object-path: private
  to-regex-range@5.0.1:
    to-regex-range: private
  to-regex@3.0.2:
    to-regex: private
  totalist@3.0.1:
    totalist: private
  traverse@0.6.9:
    traverse: private
  treemate@0.3.11:
    treemate: private
  ts-api-utils@1.3.0(typescript@5.5.4):
    ts-api-utils: private
  tslib@2.6.3:
    tslib: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  typed-array-buffer@1.0.2:
    typed-array-buffer: private
  typed-array-byte-length@1.0.1:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.2:
    typed-array-byte-offset: private
  typed-array-length@1.0.6:
    typed-array-length: private
  typedarray.prototype.slice@1.0.3:
    typedarray.prototype.slice: private
  ufo@1.5.4:
    ufo: private
  unbox-primitive@1.0.2:
    unbox-primitive: private
  unconfig@0.3.13:
    unconfig: private
  undici-types@6.11.1:
    undici-types: private
  unimport@3.9.1(rollup@3.29.4):
    unimport: private
  union-value@1.0.1:
    union-value: private
  universalify@2.0.1:
    universalify: private
  unplugin@1.12.0:
    unplugin: private
  unset-value@1.0.0:
    unset-value: private
  uri-js@4.4.1:
    uri-js: private
  urix@0.1.0:
    urix: private
  use@3.1.1:
    use: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vary@1.1.2:
    vary: private
  vdirs@0.1.8(vue@3.4.34(typescript@5.5.4)):
    vdirs: private
  vooks@0.2.12(vue@3.4.34(typescript@5.5.4)):
    vooks: private
  vue-demi@0.14.10(vue@3.4.34(typescript@5.5.4)):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@8.57.0):
    vue-eslint-parser: private
  vueuc@0.4.58(vue@3.4.34(typescript@5.5.4)):
    vueuc: private
  webpack-sources@3.2.3:
    webpack-sources: private
  webpack-virtual-modules@0.6.2:
    webpack-virtual-modules: private
  which-boxed-primitive@1.0.2:
    which-boxed-primitive: private
  which-typed-array@1.1.15:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrap-ansi@7.0.0:
    wrap-ansi: private
  wrappy@1.0.2:
    wrappy: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  y18n@5.0.8:
    y18n: private
  yargs-parser@21.1.1:
    yargs-parser: private
  yargs@17.7.2:
    yargs: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.12.1
pendingBuilds: []
prunedAt: Mon, 16 Jun 2025 01:45:09 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: https://registry.npmjs.org/
skipped:
  - '@esbuild/android-arm64@0.18.20'
  - '@esbuild/android-arm@0.18.20'
  - '@esbuild/android-x64@0.18.20'
  - '@esbuild/darwin-arm64@0.18.20'
  - '@esbuild/darwin-x64@0.18.20'
  - '@esbuild/freebsd-arm64@0.18.20'
  - '@esbuild/freebsd-x64@0.18.20'
  - '@esbuild/linux-arm64@0.18.20'
  - '@esbuild/linux-arm@0.18.20'
  - '@esbuild/linux-ia32@0.18.20'
  - '@esbuild/linux-loong64@0.18.20'
  - '@esbuild/linux-mips64el@0.18.20'
  - '@esbuild/linux-ppc64@0.18.20'
  - '@esbuild/linux-riscv64@0.18.20'
  - '@esbuild/linux-s390x@0.18.20'
  - '@esbuild/linux-x64@0.18.20'
  - '@esbuild/netbsd-x64@0.18.20'
  - '@esbuild/openbsd-x64@0.18.20'
  - '@esbuild/sunos-x64@0.18.20'
  - '@esbuild/win32-arm64@0.18.20'
  - '@esbuild/win32-ia32@0.18.20'
  - fsevents@2.3.3
storeDir: D:\.pnpm-store\v10
virtualStoreDir: D:\human\human_api_with_backend\vue-fastapi-admin\web\node_modules\.pnpm
virtualStoreDirMaxLength: 60
